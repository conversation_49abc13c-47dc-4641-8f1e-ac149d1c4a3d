# Environment Variables Setup

This project uses environment variables to securely store credentials and configuration settings. Follow these steps to set up your environment:

## Setting Up Your Environment Variables

1. Copy the sample environment file to create your own:
   ```
   cp .env.sample .env
   ```

2. Edit the `.env` file and replace the placeholder values with your actual credentials:
   ```
   # Robinhood credentials
   RH_UN=your_actual_username
   RH_PW=your_actual_password

   # Other credentials...
   ```

3. Save the file. The application will automatically load these variables when it runs.

## Available Environment Variables

- **Robinhood Credentials**:
  - `RH_UN`: Your Robinhood username
  - `RH_PW`: Your Robinhood password

- **Fidelity Credentials**:
  - `FIDELITY_COOKIE`: Your Fidelity authentication cookie
  - `FIDELITY_COOKIE_SCAN`: Your Fidelity cookie for scanning
  - `FIDELITY_FILE`: Path to your Fidelity data file

- **CoinMarketCap API**:
  - `CMC_PRO_API_KEY`: Your CoinMarketCap API key

- **AlphaVantage API**:
  - `ALPHA_VANTAGE_API_KEY`: Your AlphaVantage API key


## Security Notes

- **<PERSON><PERSON><PERSON> commit your `.env` file to version control**. It's already added to `.gitignore` to prevent accidental commits.
- Regularly rotate your credentials for better security.
- If you suspect your credentials have been compromised, change them immediately.

## Troubleshooting

If you see warnings about missing credentials:

1. Make sure your `.env` file is in the root directory of the project
2. Verify that the variable names match exactly what's in the `.env.sample` file
3. Check that there are no spaces around the `=` sign in your variable definitions
4. Ensure the `.env` file is properly formatted (no quotes around values unless they're part of the value)
