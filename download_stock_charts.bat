@echo off

rem Change to the script directory
cd /d "%~dp0"

rem Set environment variables from .env file
for /f "tokens=*" %%a in (.env) do (
    set %%a
)

rem Set PYTHONPATH to include the src directory
set PYTHONPATH=%~dp0src

rem Activate the Python 3.11 environment and run the standalone download_stock_charts script
call pythonicfin_env_py311\Scripts\activate.bat && python src\standalone_download_stock_charts.py

rem Pause the script so the window doesn't close immediately
pause
