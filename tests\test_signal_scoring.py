#!/usr/bin/env python3
"""
Test script to demonstrate the new signal scoring functionality.
This script shows how the signal scoring works with sample data.
"""

import pandas as pd
import sys
# Add src directory to Python path
sys.path.append('src')
from src.fidelity.chart_analysis.analysis import calculate_signal_score, get_signal_strength

def create_sample_screener_data():
    """Create sample screener data to test signal scoring"""

    # Sample data based on the columns you provided
    sample_data = {
        'Symbol': ['CIFR', 'IREN', 'NVDA', 'AAPL', 'TSLA'],
        'Company Name': ['Cipher Mining Inc', 'IREN Limited', 'NVIDIA Corp', 'Apple Inc', 'Tesla Inc'],
        'Security Price': [12.28, 38.64, 140.50, 225.75, 380.25],
        'Volume (90 Day Avg)': [32.48, 26.00, 45.20, 55.30, 75.40],
        '$Volume': [400.0, 1000.0, 6300.0, 12400.0, 28600.0],  # Daily dollar volume in millions
        'EPS Growth (TTM vs Prior TTM)': [-24.86, 55.20, 125.5, 15.2, 45.8],
        'EPS Growth (Last Qtr vs. Same Qtr Prior Yr)': [-140, 165.43, 89.2, 8.5, 22.1],
        'EPS Growth (Proj Next Yr vs. This Yr)': [89.02, 214.50, 45.2, 12.8, 35.6],
        'Revenue Growth (TTM vs. Prior TTM)': [0.11, 214.49, 22.5, 8.2, 18.9],
        'Equity Summary Score (ESS) from LSEG StarMine': [5.2, 7.8, 8.5, 7.2, 6.8],
        'RS_Rating': [98, 98, 95, 85, 88],
        'Price Performance (4 Weeks)': [100.85, 85.40, 15.2, 8.5, 25.8],
        'Price Performance (13 Weeks)': [211.02, 270.61, 45.8, 12.2, 35.9],
        'Price Performance (52 Weeks)': [308.62, 370.47, 185.2, 25.8, 125.6],
        'MOMO': [True, True, True, False, True],
        'Sector': ['Information Technology', 'Information Technology', 'Technology', 'Technology', 'Consumer Discretionary'],
        'Industry': ['Software', 'Software', 'Semiconductors', 'Technology Hardware', 'Automobiles']
    }

    return pd.DataFrame(sample_data)

def create_sample_technical_data():
    """Create sample technical data that would come from getStockDataV3"""

    # Sample technical data for each symbol
    technical_data = {
        'CIFR': {
            'Close': [12.28],
            '50': [10.5], '100': [9.8], '150': [9.2], '200': [8.5],
            'TTM': [True],
            'slope_50': [0.15], 'slope_200': [0.08],
            'UDRatio': [0.85],
            '$VolumeM': [45.2],
            'RS_Rating': [98]
        },
        'IREN': {
            'Close': [38.64],
            '50': [32.1], '100': [28.5], '150': [25.8], '200': [22.3],
            'TTM': [True],
            'slope_50': [0.22], 'slope_200': [0.18],
            'UDRatio': [0.92],
            '$VolumeM': [85.6],
            'RS_Rating': [98]
        },
        'NVDA': {
            'Close': [140.50],
            '50': [135.2], '100': [128.8], '150': [125.5], '200': [118.9],
            'TTM': [False],
            'slope_50': [0.05], 'slope_200': [0.12],
            'UDRatio': [0.78],
            '$VolumeM': [125.8],
            'RS_Rating': [95]
        },
        'AAPL': {
            'Close': [225.75],
            '50': [220.1], '100': [215.8], '150': [210.2], '200': [205.5],
            'TTM': [False],
            'slope_50': [0.02], 'slope_200': [0.05],
            'UDRatio': [0.68],
            '$VolumeM': [95.2],
            'RS_Rating': [85]
        },
        'TSLA': {
            'Close': [380.25],
            '50': [375.8], '100': [365.2], '150': [355.8], '200': [345.1],
            'TTM': [True],
            'slope_50': [0.08], 'slope_200': [0.15],
            'UDRatio': [0.82],
            '$VolumeM': [185.6],
            'RS_Rating': [88]
        }
    }

    # Convert to DataFrame format
    tech_dfs = {}
    for symbol, data in technical_data.items():
        tech_dfs[symbol] = pd.DataFrame(data)

    return tech_dfs

def test_signal_scoring():
    """Test the signal scoring functionality"""

    print("=== SIGNAL SCORING TEST ===\n")

    # Create sample data
    screener_df = create_sample_screener_data()
    technical_dfs = create_sample_technical_data()

    print("Testing signal scoring for sample stocks:\n")

    results = []

    for _, row in screener_df.iterrows():
        symbol = row['Symbol']
        tech_df = technical_dfs.get(symbol)

        # Calculate signal score
        signal_score = calculate_signal_score(row, tech_df)
        signal_strength = get_signal_strength(signal_score)

        results.append({
            'Symbol': symbol,
            'Company': row['Company Name'][:25],
            'Score': signal_score,
            'Signal': signal_strength,
            'Price': row['Security Price'],
            'RS_Rating': row['RS_Rating']
        })

        print(f"{symbol:6} | {signal_score:5.1f} | {signal_strength:12} | {row['Company Name'][:30]:30}")

    print("\n=== DETAILED BREAKDOWN ===")

    # Show detailed breakdown for top scorer
    top_stock = max(results, key=lambda x: x['Score'])
    print(f"\nDetailed analysis for top scorer: {top_stock['Symbol']} ({top_stock['Signal']})")
    print(f"Overall Score: {top_stock['Score']:.1f}/100")

    # You can add more detailed breakdown here if needed

    print("\n=== SIGNAL DISTRIBUTION ===")
    signal_counts = {}
    for result in results:
        signal = result['Signal']
        signal_counts[signal] = signal_counts.get(signal, 0) + 1

    for signal, count in sorted(signal_counts.items()):
        print(f"{signal:12}: {count} stocks")

if __name__ == "__main__":
    test_signal_scoring()
