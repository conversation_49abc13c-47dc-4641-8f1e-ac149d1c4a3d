#!/usr/bin/env python3
"""
Test the new fallback RS calculation for PONY
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

import pandas as pd
import yfinance as yf
from datetime import datetime, timedelta

def test_fallback_rs_calculation():
    """Test the new fallback RS calculation"""
    print("=== Testing Fallback RS Calculation ===")
    
    try:
        # Import the updated functions
        sys.path.append('src/fidelity/chart_analysis/rs')
        from build_rs_stocks import weighted_rs_score, weighted_rs_score_fallback
        
        # Get PONY data
        print("Getting PONY data...")
        pony = yf.Ticker("PONY")
        end_date = datetime.now()
        start_date = end_date - timedelta(days=365)
        pony_hist = pony.history(start=start_date, end=end_date)
        
        # Get benchmark data (SPY)
        print("Getting benchmark data...")
        spy = yf.Ticker("SPY")
        spy_hist = spy.history(start=start_date, end=end_date)
        
        # Align dates
        common_dates = pony_hist.index.intersection(spy_hist.index)
        pony_close = pony_hist.loc[common_dates, 'Close']
        spy_close = spy_hist.loc[common_dates, 'Close']
        
        print(f"PONY data: {len(pony_close)} days")
        print(f"SPY data: {len(spy_close)} days")
        print(f"Common trading days: {len(common_dates)}")
        
        # Test original function (should fail)
        print("\n--- Original RS Function ---")
        original_score = weighted_rs_score(pony_close, spy_close)
        print(f"Original RS score: {original_score}")
        
        # Test fallback function (should work)
        print("\n--- Fallback RS Function ---")
        fallback_score = weighted_rs_score_fallback(pony_close, spy_close)
        print(f"Fallback RS score: {fallback_score}")
        
        # Test the combined logic (what the actual code will do)
        print("\n--- Combined Logic (as in build_rs_stocks.py) ---")
        score = weighted_rs_score(pony_close, spy_close)
        if score is None:
            print("Original function returned None, trying fallback...")
            score = weighted_rs_score_fallback(pony_close, spy_close)
        
        if score is not None:
            print(f"✓ Final PONY RS Score: {score:.2f}")
            return True
        else:
            print("❌ Both functions failed")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_other_adrs():
    """Test that other ADRs still work with the new logic"""
    print("\n=== Testing Other ADRs with New Logic ===")
    
    try:
        sys.path.append('src/fidelity/chart_analysis/rs')
        from build_rs_stocks import weighted_rs_score, weighted_rs_score_fallback
        
        adrs = ['BABA', 'JD', 'BIDU']  # These should work with original function
        
        # Get benchmark data
        spy = yf.Ticker("SPY")
        end_date = datetime.now()
        start_date = end_date - timedelta(days=365)
        spy_hist = spy.history(start=start_date, end=end_date)
        
        for symbol in adrs:
            print(f"\n--- {symbol} ---")
            try:
                ticker = yf.Ticker(symbol)
                hist = ticker.history(start=start_date, end=end_date)
                
                # Align dates
                common_dates = hist.index.intersection(spy_hist.index)
                stock_close = hist.loc[common_dates, 'Close']
                spy_close = spy_hist.loc[common_dates, 'Close']
                
                # Test combined logic
                score = weighted_rs_score(stock_close, spy_close)
                if score is None:
                    score = weighted_rs_score_fallback(stock_close, spy_close)
                
                if score is not None:
                    print(f"  RS Score: {score:.2f}")
                    print(f"  Data days: {len(hist)}")
                    print(f"  Used: {'Original' if weighted_rs_score(stock_close, spy_close) is not None else 'Fallback'}")
                else:
                    print(f"  ❌ Both functions failed")
                    
            except Exception as e:
                print(f"  ❌ Error: {e}")
                
    except Exception as e:
        print(f"❌ Error in ADR test: {e}")

if __name__ == "__main__":
    print("=" * 60)
    print("TESTING FALLBACK RS CALCULATION")
    print("=" * 60)
    
    success = test_fallback_rs_calculation()
    test_other_adrs()
    
    print("\n" + "=" * 60)
    print("SUMMARY")
    print("=" * 60)
    if success:
        print("✓ Fallback RS calculation works for PONY!")
        print("  Now run the full RS analysis to see if PONY appears in output")
    else:
        print("❌ Fallback calculation failed")
        print("  Check the error messages above")
