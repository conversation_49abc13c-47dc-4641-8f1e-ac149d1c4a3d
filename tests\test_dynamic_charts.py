#!/usr/bin/env python3
"""
Test script for dynamic chart ID generation
"""

import asyncio
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_dynamic_chart_ids():
    """Test the dynamic chart ID generation system"""
    
    print("🧪 TESTING DYNAMIC CHART ID SYSTEM")
    print("=" * 50)
    
    try:
        from fidelity.chart_analysis.dynamic_chart_ids import initialize_chart_ids, refresh_chart_ids
        
        print("✅ Successfully imported dynamic chart ID module")
        
        # Test initial generation
        print("\n📊 Testing initial chart ID generation...")
        daily_id, weekly_id = await initialize_chart_ids()
        
        print(f"\n📈 Results:")
        print(f"Daily Chart ID:  {daily_id or 'Failed'}")
        print(f"Weekly Chart ID: {weekly_id or 'Failed'}")
        
        if daily_id and weekly_id:
            print("\n✅ Both chart IDs generated successfully!")
            
            # Show the URL templates
            print(f"\n🔗 URL Templates:")
            print(f"Daily:  https://stockcharts.com/c-sc/sc?s=SYMBOL&p=D&yr=0&mn=6&dy=0&i={daily_id}&r=tdy")
            print(f"Weekly: https://stockcharts.com/c-sc/sc?s=SYMBOL&p=W&yr=2&mn=6&dy=0&i={weekly_id}&r=tdy")
            
            # Test refresh functionality
            print(f"\n🔄 Testing chart ID refresh...")
            new_daily_id, new_weekly_id = await refresh_chart_ids()
            
            print(f"\n📈 Refresh Results:")
            print(f"New Daily ID:  {new_daily_id or 'Failed'}")
            print(f"New Weekly ID: {new_weekly_id or 'Failed'}")
            
            if new_daily_id and new_weekly_id:
                print("✅ Chart ID refresh successful!")
                
                # Check if IDs changed (they might be the same if cached)
                if new_daily_id != daily_id or new_weekly_id != weekly_id:
                    print("🔄 Chart IDs were refreshed with new values")
                else:
                    print("📋 Chart IDs remained the same (cached or identical configuration)")
            else:
                print("❌ Chart ID refresh failed")
        else:
            print("❌ Initial chart ID generation failed")
            
    except ImportError as e:
        print(f"❌ Could not import dynamic chart ID module: {e}")
        print("Make sure the sc-scrapper directory and get_sc_id.py are available")
        return False
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        return False
    
    print(f"\n🎯 Test completed!")
    return True

async def test_web_ui_integration():
    """Test the web UI integration"""
    
    print("\n🌐 TESTING WEB UI INTEGRATION")
    print("=" * 50)
    
    try:
        from fidelity.chart_analysis.web_ui import setup_dynamic_chart_ids, DAILY_CHART_ID, WEEKLY_CHART_ID
        
        print("✅ Successfully imported web UI module")
        
        # Test the setup function
        print("\n🔧 Testing web UI chart ID setup...")
        await setup_dynamic_chart_ids()
        
        print(f"\n📊 Web UI Chart IDs:")
        print(f"DAILY_CHART_ID:  {DAILY_CHART_ID or 'Not set'}")
        print(f"WEEKLY_CHART_ID: {WEEKLY_CHART_ID or 'Not set'}")
        
        if DAILY_CHART_ID and WEEKLY_CHART_ID:
            print("✅ Web UI chart IDs successfully configured!")
            return True
        else:
            print("❌ Web UI chart IDs not properly set")
            return False
            
    except Exception as e:
        print(f"❌ Error testing web UI integration: {e}")
        return False

if __name__ == "__main__":
    async def main():
        print("🚀 DYNAMIC STOCKCHARTS ID INTEGRATION TEST")
        print("=" * 60)
        
        # Test the core functionality
        success1 = await test_dynamic_chart_ids()
        
        # Test web UI integration
        success2 = await test_web_ui_integration()
        
        print(f"\n" + "=" * 60)
        if success1 and success2:
            print("🎉 ALL TESTS PASSED!")
            print("\nYour dynamic chart ID system is ready to use:")
            print("• Daily charts: Default settings (EMA 20, SMA 50, 100)")
            print("• Weekly charts: SMA 10, 20 as requested")
            print("• Web UI integration: Automatic initialization")
            print("• Manual refresh: Available via /refresh-chart-ids endpoint")
        else:
            print("❌ SOME TESTS FAILED")
            print("\nPlease check:")
            print("• sc-scrapper/get_sc_id.py is available")
            print("• Playwright is installed and working")
            print("• StockCharts.com is accessible")
            
        print(f"\n📝 Next Steps:")
        print("1. Start your web UI server")
        print("2. Chart IDs will be automatically generated on startup")
        print("3. Use the '🔄 Charts' button to refresh IDs if needed")
        print("4. Charts will use the dynamic IDs for daily/weekly views")
    
    asyncio.run(main())
