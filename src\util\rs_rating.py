from util.util import loadMovingAverages, loadMovingAveragesV2
import yfinance as yf
import pandas as pd
import numpy as np
from pandas_datareader import data as pdr
from datetime import datetime
from yahoo_fin import stock_info as stkinfo
from common.http_session import get_session
# Disable SSL verification to fix certificate issues
session = get_session('src/certs/my_ca_bundle.pem')


class RSCalculator:
    def __init__(self, rs_data_path: str):
        """Initialize RS Calculator with historical RS ratings data"""
        self.sp500 = None
        self.sp500_performance = None
        self.rs_env = self._load_rs_environment(rs_data_path)

    def _load_rs_environment(self, csv_path: str):
        """Load and process RS environment data from the new format"""
        df = pd.read_csv(csv_path)
        # Get the last row
        last_row = df.iloc[-1]
        # Extract the desired columns
        desired_columns = ['P98', 'P90', 'P70', 'P50', 'P30', 'P10', 'P02']
        environment_values = last_row[desired_columns].tolist()
        return environment_values

    def _ensure_sp500_data(self, period: str):
        """Ensure SP500 data is loaded and current"""
        if self.sp500 is None:
            # Explicitly set auto_adjust=False for consistency
            self.sp500 = yf.download("^GSPC", period=period, interval='1d', auto_adjust=False,session=session)
            self._calculate_sp500_performance()

    def _calculate_sp500_performance(self):
        """Calculate SP500 performance metrics"""
        self.sp500_performance = {}
        for days in [63, 126, 189, 252]:
            n_days = min(days, len(self.sp500) - 1)
            # Use .item() to extract scalar value from Series
            close_latest = self.sp500['Close'].iloc[-1].item()
            close_past = self.sp500['Close'].iloc[-1-n_days].item()
            if close_past == 0 or pd.isna(close_past):
                 print(f"Warning: SP500 Past Close is zero or NaN for {days} days lookback.")
                 self.sp500_performance[days] = np.nan # Avoid division by zero/NaN
            elif pd.isna(close_latest):
                 print(f"Warning: SP500 Latest Close is NaN for {days} days lookback.")
                 self.sp500_performance[days] = np.nan
            else:
                self.sp500_performance[days] = close_latest / close_past


    def _calculate_stock_performance(self, stock_data):
        """Calculate stock performance metrics"""
        performance = {}
        for days in [63, 126, 189, 252]:
            n_days = min(days, len(stock_data) - 1)
            # Use .item() to extract scalar value from Series
            close_latest = stock_data['Close'].iloc[-1].item()
            close_past = stock_data['Close'].iloc[-1-n_days].item()
            if close_past == 0 or pd.isna(close_past):
                 print(f"Warning: Stock Past Close is zero or NaN for {days} days lookback.")
                 performance[days] = np.nan # Avoid division by zero/NaN
            elif pd.isna(close_latest):
                 print(f"Warning: Stock Latest Close is NaN for {days} days lookback.")
                 performance[days] = np.nan
            else:
                performance[days] = close_latest / close_past
        return performance

    def _attribute_percentile(self, total_rs_score: float, taller_perf: float,
                            smaller_perf: float, range_up: float,
                            range_dn: float, weight: float) -> float:
        """Calculate percentile using linear approximation"""
        sum_score = total_rs_score + (total_rs_score - smaller_perf) * weight
        sum_score = min(sum_score, taller_perf - 1)

        k1 = smaller_perf / range_dn
        k2 = (taller_perf - 1) / range_up
        k3 = (k1 - k2) / (taller_perf - 1 - smaller_perf)

        rs_rating = sum_score / (k1 - k3 * (total_rs_score - smaller_perf))
        return max(min(rs_rating, range_up), range_dn)

    def calculate_rs_rating(self, stock_data: pd.DataFrame, period: str) -> float:
        """Calculate RS Rating for given stock data"""
        self._ensure_sp500_data(period)

        # Calculate performances
        stock_perf = self._calculate_stock_performance(stock_data)

        # Calculate weighted performances
        rs_stock = (0.4 * stock_perf[63] +
                   0.2 * stock_perf[126] +
                   0.2 * stock_perf[189] +
                   0.2 * stock_perf[252])

        rs_ref = (0.4 * self.sp500_performance[63] +
                 0.2 * self.sp500_performance[126] +
                 0.2 * self.sp500_performance[189] +
                 0.2 * self.sp500_performance[252])

        total_rs_score = (rs_stock / rs_ref) * 100

        # Ensure total_rs_score is a scalar float before comparison
        if isinstance(total_rs_score, pd.Series):
            if len(total_rs_score) == 1:
                total_rs_score = total_rs_score.item()
            else:
                # This case is unexpected based on the calculation logic.
                raise TypeError(f"Expected total_rs_score to be scalar, but got a Series: {total_rs_score}")
        elif not isinstance(total_rs_score, (int, float)):
             raise TypeError(f"Expected total_rs_score to be numeric, but got type {type(total_rs_score)}")


        # Calculate final rating
        thresholds = self.rs_env

        if total_rs_score >= thresholds[0]:
            return 99
        elif total_rs_score <= thresholds[6]:
            return 1

        # Define rating ranges
        ranges = [
            (thresholds[0], thresholds[1], 98, 90, 0.33),
            (thresholds[1], thresholds[2], 89, 70, 2.1),
            (thresholds[2], thresholds[3], 69, 50, 0),
            (thresholds[3], thresholds[4], 49, 30, 0),
            (thresholds[4], thresholds[5], 29, 10, 0),
            (thresholds[5], thresholds[6], 9, 2, 0)
        ]

        for upper, lower, range_up, range_dn, weight in ranges:
            if lower <= total_rs_score < upper:
                return self._attribute_percentile(
                    total_rs_score, upper, lower, range_up, range_dn, weight
                )

        return -1
