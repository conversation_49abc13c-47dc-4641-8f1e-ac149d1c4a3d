@echo off
REM Set SSL certificate file path for Python requests
set SSL_CERT_FILE=%~dp0src\certs\my_ca_bundle.pem
set REQUESTS_CA_BUNDLE=%~dp0src\certs\my_ca_bundle.pem
set CURL_CA_BUNDLE=%~dp0src\certs\my_ca_bundle.pem

REM Disable SSL verification warnings
set PYTHONWARNINGS=ignore:Unverified HTTPS request

REM Set Python path to include src directory
set PYTHONPATH=%~dp0;%~dp0src;%PYTHONPATH%

REM Run the test script with the environment variables set
call pythonicfin_env_py311\Scripts\python.exe "%~dp0test_ssl_fix_complete.py"

REM Pause to see the results
pause
