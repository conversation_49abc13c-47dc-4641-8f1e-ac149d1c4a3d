@echo off

rem Change to the script directory
cd /d "%~dp0"

rem Add src to PYTHONPATH
set "PYTHONPATH=%CD%\src;%PYTHONPATH%"

rem Activate the Python 3.11 environment and run the multi-file low volume consolidation scanner
echo Starting Multi-File Low Volume Consolidation Scanner...
echo Processing all Excel files from data\screener directory...
echo Default threshold: 30%% (use --threshold 0.4 for 40%%, etc.)
echo ========================================================
call pythonicfin_env_py311\Scripts\activate.bat && python -u -m fidelity.scanner.low_volume_consolidation_scanner %*

rem Pause the script so the window doesn't close immediately
pause
