import asyncio
from playwright.async_api import async_playwright

START_URL = "https://stockcharts.com/sc3/ui"

async def dump_selects(scope, scope_name):
    print(f"\n=== {scope_name} ===")
    selects = scope.locator("select")
    count = await selects.count()
    for i in range(count):
        sel = selects.nth(i)
        id_ = await sel.get_attribute("id")
        name = await sel.get_attribute("name")
        # try to find a label bound to it
        label_txt = ""
        if id_:
            label = scope.locator(f"label[for='{id_}']")
            if await label.count():
                label_txt = (await label.first.inner_text()).strip()
        # otherwise, try the nearest previous label-ish element
        if not label_txt:
            prev_label = sel.locator("xpath=preceding::label[1]")
            if await prev_label.count():
                label_txt = (await prev_label.first.inner_text()).strip()
        # sample first few option texts
        options = await sel.locator("option").all_text_contents()
        options = [o.strip() for o in options]
        print(f"- select id={id_!r} name={name!r} label≈{label_txt!r}")
        print(f"  options sample: {options[:8]}")

async def main():
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False, slow_mo=200)  # show UI, slow for visibility
        page = await browser.new_page()
        await page.goto(START_URL)

        # dump main page selects
        await dump_selects(page, "MAIN PAGE")

        # dump all frames
        for f in page.frames:
            try:
                url = f.url
            except Exception:
                url = "<no url>"
            print(f"\n--- Frame: {url} ---")
            await dump_selects(f, f"FRAME {url}")

        print("\nTip: Look for the select that contains options like 'Candlesticks', 'OHLC Bars', etc.")
        # keep window open a moment
        await page.wait_for_timeout(5000)
        await browser.close()

if __name__ == "__main__":
    asyncio.run(main())
