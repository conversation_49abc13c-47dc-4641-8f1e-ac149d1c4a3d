#!/usr/bin/env python3
"""
Test script to demonstrate the new cookie management features
in the Fidelity Portfolio Alerter.
"""

import sys
import os

# Add project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__))))

from src.fidelity.portfolio_alerter import (
    get_env_variables, 
    test_cookie_validity, 
    prompt_for_new_cookie,
    update_cookie_in_env,
    send_cookie_expired_alert
)

def main():
    print("🧪 Testing Cookie Management Features")
    print("=" * 50)
    
    try:
        # Test 1: Load environment variables
        print("\n1. Testing environment variable loading...")
        cookie, account_number = get_env_variables()
        print(f"✅ Loaded cookie (length: {len(cookie)})")
        print(f"✅ Loaded account number: {account_number}")
        
        # Test 2: Test cookie validity
        print("\n2. Testing cookie validity...")
        is_valid, message = test_cookie_validity(cookie, account_number)
        if is_valid:
            print(f"✅ {message}")
        else:
            print(f"❌ {message}")
            
            # Test 3: Demonstrate cookie expiration alert
            print("\n3. Demonstrating cookie expiration alert...")
            send_cookie_expired_alert()
            
            # Test 4: Simulate cookie refresh (commented out to avoid actual prompt)
            print("\n4. <PERSON>ie refresh would be prompted here...")
            print("   (Skipping interactive prompt in test)")
            # new_cookie = prompt_for_new_cookie()
            # if new_cookie:
            #     update_cookie_in_env(new_cookie)
        
        print("\n✅ All tests completed!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
