#!/usr/bin/env python3
"""
Add Description column to screener Excel file.
This script reads the screener file, fetches company descriptions using yfinance,
and adds them as a new column.
"""

import pandas as pd
import yfinance as yf
import asyncio
from functools import partial
import sys
import os
from datetime import datetime

# Add src to path
sys.path.insert(0, 'src')

try:
    from common.http_session import get_session
    session = get_session('src/certs/my_ca_bundle.pem')
except ImportError:
    print("Warning: Could not import session. Using default session.")
    session = None

async def _run_sync_in_executor(func, *args, **kwargs):
    """Run synchronous function in executor to make it async."""
    loop = asyncio.get_running_loop()
    return await loop.run_in_executor(None, partial(func, *args, **kwargs))

async def fetch_description(symbol: str) -> str:
    """
    Fetch company description using yfinance.
    
    Returns:
    - Company description string
    """
    try:
        ticker_data = await _run_sync_in_executor(yf.Ticker, symbol, session=session)
        info = await _run_sync_in_executor(lambda: ticker_data.info)
        
        description = info.get('longBusinessSummary', 'N/A')
        return description if description and description != 'N/A' else 'No description available'
        
    except Exception as e:
        print(f"Error fetching description for {symbol}: {e}")
        return f'Error fetching description: {str(e)}'

async def add_descriptions_to_screener(file_path: str):
    """
    Add descriptions to screener Excel file.
    
    Args:
    - file_path: Path to the Excel file
    """
    print(f"Reading screener file: {file_path}")
    
    try:
        # Read the Excel file
        df = pd.read_excel(file_path)
        print(f"Loaded {len(df)} rows from screener file")
        
        # Display current columns
        print(f"Current columns: {list(df.columns)}")
        
        # Find the symbol column (could be 'Symbol', 'Ticker', etc.)
        symbol_col = None
        for col in df.columns:
            if col.lower() in ['symbol', 'ticker', 'stock']:
                symbol_col = col
                break
        
        if symbol_col is None:
            print("Error: Could not find symbol/ticker column in the file")
            print("Available columns:", list(df.columns))
            return
        
        print(f"Using '{symbol_col}' as the symbol column")
        
        # Check if Description column already exists
        if 'Description' in df.columns:
            print("Description column already exists. Updating...")
        else:
            print("Adding new Description column...")
            df['Description'] = ''
        
        # Get unique symbols to avoid duplicate API calls
        symbols = df[symbol_col].unique()
        print(f"Found {len(symbols)} unique symbols to process")
        
        # Fetch descriptions for each symbol
        descriptions = {}
        for i, symbol in enumerate(symbols, 1):
            if pd.isna(symbol) or symbol == '':
                continue
                
            print(f"[{i}/{len(symbols)}] Fetching description for {symbol}...")
            description = await fetch_description(str(symbol).strip().upper())
            descriptions[symbol] = description
            
            # Small delay to be respectful to APIs
            await asyncio.sleep(0.1)
        
        # Map descriptions back to the dataframe
        df['Description'] = df[symbol_col].map(descriptions).fillna('No description available')
        
        # Create output filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_name = os.path.splitext(os.path.basename(file_path))[0]
        output_file = f"src/data/screener/{base_name}_with_descriptions_{timestamp}.xlsx"
        
        # Save the updated file
        df.to_excel(output_file, index=False)
        print(f"\nUpdated screener saved to: {output_file}")
        
        # Display sample results
        print(f"\n=== Sample Results (first 5 rows) ===")
        display_cols = [symbol_col, 'Description']
        if len(df.columns) > 2:
            # Add a couple more columns for context
            other_cols = [col for col in df.columns if col not in display_cols][:2]
            display_cols.extend(other_cols)
        
        sample_df = df[display_cols].head(5).copy()
        # Truncate descriptions for display
        if 'Description' in sample_df.columns:
            sample_df['Description'] = sample_df['Description'].apply(
                lambda x: x[:100] + '...' if len(str(x)) > 100 else x
            )
        
        print(sample_df.to_string(index=False))
        
        return output_file
        
    except Exception as e:
        print(f"Error processing file: {e}")
        import traceback
        traceback.print_exc()
        return None

async def main():
    """Main function."""
    print("=== Add Descriptions to Screener ===\n")
    
    # Default file path
    file_path = "src/data/screener/filtered_sorted_screener_2025-09-24.xlsx"
    
    # Check if file exists
    if not os.path.exists(file_path):
        print(f"Error: File not found: {file_path}")
        
        # List available screener files
        screener_dir = "src/data/screener"
        if os.path.exists(screener_dir):
            print(f"\nAvailable screener files in {screener_dir}:")
            for file in os.listdir(screener_dir):
                if file.endswith('.xlsx') or file.endswith('.xls'):
                    print(f"  - {file}")
        return
    
    # Process the file
    output_file = await add_descriptions_to_screener(file_path)
    
    if output_file:
        print(f"\n✅ Successfully added descriptions to screener!")
        print(f"📁 Output file: {output_file}")
    else:
        print("\n❌ Failed to process screener file.")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nOperation cancelled by user.")
    except Exception as e:
        print(f"\nError: {e}")
        import traceback
        traceback.print_exc()
