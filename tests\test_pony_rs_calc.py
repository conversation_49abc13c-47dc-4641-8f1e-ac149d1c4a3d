#!/usr/bin/env python3
"""
Test script to debug why <PERSON><PERSON><PERSON> is not appearing in the final RS output
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

import pandas as pd
import yfinance as yf
from datetime import datetime, timedelta

def test_pony_data_availability():
    """Test if PONY data is available from Yahoo Finance"""
    print("=== Testing PONY Data Availability ===")
    
    try:
        # Test basic ticker info
        pony = yf.Ticker("PONY")
        info = pony.info
        print(f"✓ PONY ticker info available")
        print(f"  Name: {info.get('longName', 'N/A')}")
        print(f"  Exchange: {info.get('exchange', 'N/A')}")
        print(f"  Currency: {info.get('currency', 'N/A')}")
        print(f"  Market Cap: {info.get('marketCap', 'N/A')}")
        
        # Test historical data
        end_date = datetime.now()
        start_date = end_date - timedelta(days=365)  # 1 year of data
        
        hist = pony.history(start=start_date, end=end_date)
        print(f"✓ Historical data available: {len(hist)} days")
        print(f"  Date range: {hist.index.min()} to {hist.index.max()}")
        print(f"  Latest close: ${hist['Close'].iloc[-1]:.2f}")
        print(f"  Average volume: {hist['Volume'].mean():.0f}")
        
        return True, hist
        
    except Exception as e:
        print(f"❌ Error getting PONY data: {e}")
        return False, None

def test_pony_rs_calculation():
    """Test RS calculation specifically for PONY"""
    print("\n=== Testing PONY RS Calculation ===")
    
    try:
        # Import the RS calculation functions
        sys.path.append('src/fidelity/chart_analysis/rs')
        from build_rs_stocks import calculate_relative_strength, get_benchmark_data
        
        # Get benchmark data (SPY)
        print("Getting benchmark data...")
        benchmark_data = get_benchmark_data()
        print(f"✓ Benchmark data: {len(benchmark_data)} days")
        
        # Get PONY data
        print("Getting PONY data...")
        pony_data_available, pony_hist = test_pony_data_availability()
        
        if not pony_data_available:
            print("❌ Cannot calculate RS - PONY data not available")
            return False
        
        # Calculate RS
        print("Calculating relative strength...")
        rs_score = calculate_relative_strength(pony_hist['Close'], benchmark_data)
        print(f"✓ PONY RS Score: {rs_score}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error calculating RS for PONY: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pony_filtering_steps():
    """Test each filtering step in build_rs_stocks.py for PONY"""
    print("\n=== Testing PONY Filtering Steps ===")
    
    try:
        # Check if PONY is in universe
        from fidelity.chart_analysis.rs.universe_from_symbol_dir import load_stocks_only_from_symbol_dir
        universe_df = load_stocks_only_from_symbol_dir()
        pony_in_universe = universe_df[universe_df['Symbol'] == 'PONY']
        
        print(f"Step 1 - Universe: {'✓ PASSED' if len(pony_in_universe) > 0 else '❌ FAILED'}")
        if len(pony_in_universe) > 0:
            print(f"  PONY found in universe: {pony_in_universe.iloc[0]['SecurityName']}")
        
        # Test volume and price filters (simulate build_rs_stocks.py logic)
        print("\nStep 2 - Data Download & Filtering:")
        
        # Get PONY data
        pony_data_available, pony_hist = test_pony_data_availability()
        if not pony_data_available:
            print("❌ FAILED - No data available")
            return False
        
        # Check volume filter (typical minimum is around 100k-500k average volume)
        avg_volume = pony_hist['Volume'].mean()
        volume_threshold = 100000  # Adjust based on actual threshold in build_rs_stocks.py
        volume_passed = avg_volume >= volume_threshold
        print(f"  Volume filter: {'✓ PASSED' if volume_passed else '❌ FAILED'}")
        print(f"    Average volume: {avg_volume:.0f} (threshold: {volume_threshold})")
        
        # Check price filter (typical range is $1-$1000)
        latest_price = pony_hist['Close'].iloc[-1]
        price_passed = 1.0 <= latest_price <= 1000.0
        print(f"  Price filter: {'✓ PASSED' if price_passed else '❌ FAILED'}")
        print(f"    Latest price: ${latest_price:.2f}")
        
        # Check data sufficiency (need enough days for RS calculation)
        data_days = len(pony_hist)
        min_days = 252  # Typical requirement for 1 year of data
        data_passed = data_days >= min_days
        print(f"  Data sufficiency: {'✓ PASSED' if data_passed else '❌ FAILED'}")
        print(f"    Available days: {data_days} (minimum: {min_days})")
        
        # Check for missing data
        missing_data = pony_hist['Close'].isna().sum()
        missing_passed = missing_data == 0
        print(f"  Missing data check: {'✓ PASSED' if missing_passed else '❌ FAILED'}")
        print(f"    Missing days: {missing_data}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in filtering test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pony_vs_working_adrs():
    """Compare PONY with ADRs that are working (BABA, JD)"""
    print("\n=== Comparing PONY vs Working ADRs ===")
    
    working_adrs = ['BABA', 'JD']
    all_symbols = ['PONY'] + working_adrs
    
    for symbol in all_symbols:
        print(f"\n--- {symbol} ---")
        try:
            ticker = yf.Ticker(symbol)
            
            # Get recent data
            end_date = datetime.now()
            start_date = end_date - timedelta(days=365)
            hist = ticker.history(start=start_date, end=end_date)
            
            if len(hist) > 0:
                print(f"  Data days: {len(hist)}")
                print(f"  Latest price: ${hist['Close'].iloc[-1]:.2f}")
                print(f"  Avg volume: {hist['Volume'].mean():.0f}")
                print(f"  Date range: {hist.index.min().date()} to {hist.index.max().date()}")
            else:
                print(f"  ❌ No data available")
                
        except Exception as e:
            print(f"  ❌ Error: {e}")

if __name__ == "__main__":
    print("=" * 60)
    print("DEBUGGING PONY RS CALCULATION")
    print("=" * 60)
    
    # Run all tests
    test1 = test_pony_data_availability()
    test2 = test_pony_rs_calculation()
    test3 = test_pony_filtering_steps()
    test_pony_vs_working_adrs()
    
    print("\n" + "=" * 60)
    print("SUMMARY")
    print("=" * 60)
    print("This should help identify why PONY is not in the final RS output.")
    print("Check the output above for any failed steps.")
