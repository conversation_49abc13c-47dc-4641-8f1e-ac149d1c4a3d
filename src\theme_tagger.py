#!/usr/bin/env python3
"""
Theme Tagger for Stock Symbols
Automatically tags stock symbols with relevant themes based on company descriptions.
"""

import re
import asyncio
import pandas as pd
import yfinance as yf
from collections import defaultdict
from typing import Dict, List, Tuple
import aiohttp
from functools import partial
from datetime import datetime
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import the session from common module
try:
    from common.http_session import get_session
    session = get_session('src/certs/my_ca_bundle.pem')
except ImportError:
    print("Warning: Could not import session. Using default session.")
    session = None

# ============== THEME → KEYWORDS MAP ==============

THEME_KEYWORDS = {
    # --- AI & ML ---
    "AI Infrastructure": [
        "ai infra", "foundation model", "model training", "inference", "ml ops", "model ops",
        "ai platform", "vector database", "retrieval augmented generation", "rag", "ai data platform"
    ],
    "Generative AI": [
        "generative ai", "genai", "ai agent", "text-to-image", "text-to-video",
        "ai companion", "conversational ai", "large language model", "llm"
    ],
    "Edge AI": ["edge ai", "on-device ai", "embedded ai"],
    "AI Chips": ["gpu", "asic", "fpga", "ai chip", "neural engine", "tpu", "npu"],
    "AI Safety & Ethics": [
        "ai safety", "alignment", "ai governance", "bias mitigation", "explainable ai", "xai", "ai regulation"
    ],
    "Multimodal AI": [
        "multimodal ai", "vision-language model", "vlm", "text-to-3d", "audio generation"
    ],

    # --- Autonomous Mobility / Robotics ---
    "Autonomous Driving": [
        "autonomous driving", "self-driving", "autonomous vehicle", "av software", "driverless"
    ],
    "Robo Taxi": ["robotaxi", "robo taxi", "autonomous ride-hailing", "driverless ride"],
    "Autonomous Trucks": ["robotruck", "autonomous trucking", "self-driving truck"],
    "Autonomous Delivery": ["autonomous delivery", "delivery robot", "last-mile robot"],
    "Smart Cities (V2X)": [
        "v2x", "vehicle-to-everything", "smart road", "smart traffic", "connected vehicle",
        "infrastructure-enabled autonomy"
    ],
    "Industrial Robotics": ["industrial robot", "cobot", "factory automation", "warehouse automation"],
    "Surgical Robotics": ["surgical robot", "robotic surgery"],
    "Process Automation": ["rpa", "process automation", "workflow automation"],

    # --- Energy & Climate ---
    "Clean Energy": [
        "renewable", "solar", "wind", "hydro", "geothermal", "nuclear fission", "small modular reactor", "smr"
    ],
    "Energy Storage": ["battery storage", "energy storage", "grid-scale battery", "lithium-ion"],
    "Green Hydrogen": ["green hydrogen", "electrolyzer"],
    "Carbon Capture": ["carbon capture", "ccus"],
    "EV Platforms": ["ev platform", "electric vehicle platform"],
    "EV Charging Infra": ["charging network", "ev charging", "dc fast charging"],
    "Battery Tech": ["battery cell", "solid-state battery", "battery chemistry"],
    "Nuclear Fusion": ["nuclear fusion", "fusion energy", "tokamak", "stellarator", "inertial confinement"],
    "Sustainable Materials": [
        "sustainable materials", "biomaterials", "recycled plastics", "carbon-negative materials", "circular economy"
    ],

    # --- Semiconductors & Compute ---
    "Semiconductors": [
        "semiconductor", "eda", "chip design", "foundry", "fab", "chiplet", "advanced packaging",
        "3d stacking", "euv lithography"
    ],
    "Photonics / Optical Compute": ["silicon photonics", "optical interconnect", "co-packaged optics"],

    # --- Quantum & Advanced Compute ---
    "Quantum Computing": [
        "quantum computing", "quantum processor", "quantum algorithm", "quantum hardware", "qpu"
    ],
    "Quantum Networking": ["quantum internet", "quantum communication", "quantum encryption"],
    "Quantum Sensing": ["quantum sensor", "quantum metrology", "qv", "quantum advantage in sensing"],

    # --- Connectivity & Cloud ---
    "5G/6G": ["5g", "6g", "network slicing", "massive mimo", "terahertz", "thz communication", "satellite 5g integration"],
    "Edge Computing": ["edge computing", "multi-access edge", "mec"],
    "Cloud Infrastructure": ["cloud infrastructure", "distributed cloud"],

    # --- Cybersecurity ---
    "Cybersecurity": [
        "cybersecurity", "threat detection", "zero trust", "identity management", "iam",
        "ai security", "adversarial attack", "ml security"
    ],
    "Post-Quantum Security": ["post-quantum", "pq encryption", "pq cryptography"],

    # --- Biotech & Health ---
    "Genomics & Precision Medicine": ["genomics", "gene editing", "crispr", "precision medicine", "mrna"],
    "Digital Health": [
        "digital health", "telehealth", "remote patient monitoring", "wearables",
        "health ai", "predictive analytics", "personalized nutrition"
    ],
    "Synthetic Biology / Bioengineering": [
        "synthetic biology", "bioengineering", "cell therapy", "organoid", "bioprinting"
    ],
    "Neurotech / Brain-Computer Interfaces": [
        "neurotech", "bci", "brain-computer interface", "neural implant", "eeg", "neural lace"
    ],
    "AI-Driven Drug Discovery": [
        "ai drug discovery", "protein folding", "alpha fold", "in silico trials", "virtual screening"
    ],

    # --- Blockchain / Crypto ---
    "Smart Contract Platforms (L1)": [
        "smart contract platform", "layer 1", "l1", "evm", "account abstraction"
    ],
    "Scaling (L2 / Rollups)": [
        "layer 2", "l2", "rollup", "optimistic rollup", "zk-rollup", "validium", "plasma",
        "data availability", "da layer", "sequencer"
    ],
    "Modular / Appchains": ["modular blockchain", "appchain", "cosmos sdk", "parachain", "subnet"],
    "Interoperability / Bridges": ["interoperability", "bridge", "ibc", "cross-chain"],
    "Oracles": ["oracle network", "price oracle", "data oracle"],
    "Data Availability": ["data availability", "blob", "danksharding", "celestia", "da service"],
    "Decentralized Storage": ["decentralized storage", "ipfs", "filecoin", "arweave"],
    "Decentralized Compute": ["decentralized compute", "distributed compute", "gpu marketplace", "verifiable compute"],
    "DePIN (Physical Infra)": [
        "depin", "decentralized physical infrastructure", "shared hotspots", "edge network", "sensor network"
    ],
    "MEV / Block Building": ["mev", "block builder", "order flow auction", "relay", "proposer-builder separation"],
    "Decentralized AI": [
        "decentralized ai", "federated learning", "ai dao", "crypto ai", "on-chain ml", "verifiable inference"
    ],

    # DeFi verticals
    "DeFi: DEX & AMM": ["dex", "amm", "decentralized exchange", "liquidity pool", "liquidity provider"],
    "DeFi: Lending & Borrowing": ["defi lending", "overcollateralized lending", "money market", "collateralized debt"],
    "DeFi: Derivatives": ["perpetuals", "on-chain derivatives", "options protocol", "futures protocol"],
    "DeFi: Asset Management": ["yield aggregator", "vaults", "index protocol", "rebalancing"],
    "Stablecoins & Payments": ["stablecoin", "payment rail", "on-chain payments", "remittance", "merchant crypto"],
    "RWA / Tokenization": ["real-world assets", "rwa", "asset tokenization", "on-chain treasuries"],
    "Staking & Liquid Staking": ["staking", "liquid staking", "lsd", "restaking", "activen", "validator services"],

    # CeFi & institutional
    "CeFi: Exchanges & Brokers": ["crypto exchange", "centralized exchange", "cex", "brokerage"],
    "Custody / MPC": ["custody", "mpc", "multi-party computation", "qualified custodian", "cold storage"],
    "Market Making & Prime": ["market maker", "prime brokerage", "liquidity provider (cefi)"],
    "Compliance / Chain Analytics": ["on-chain analytics", "aml", "kyc", "transaction screening", "travel rule"],
    "CBDCs": ["central bank digital currency", "cbdc"],

    # Apps / consumer
    "NFTs & Collectibles": ["nft", "collectible", "pfp", "on-chain art", "digital collectible"],
    "Web3 Gaming": ["play-to-earn", "gamefi", "on-chain game", "web3 gaming"],
    "Identity & Credentials": ["decentralized identity", "did", "verifiable credential", "soulbound"],
    "Wallets": ["crypto wallet", "smart wallet", "keyless wallet", "account abstraction wallet"],
    "Privacy / ZK": ["zero-knowledge", "zk", "privacy coin", "zk-snark", "zk-stark"],

    # --- Space / Aerospace ---
    "Space Infrastructure": [
        "satellite", "earth observation", "launch services", "in-space manufacturing",
        "space station", "lunar mission", "mars mission"
    ],
    "Satellite Internet": ["satellite internet", "low-earth orbit", "leo satellite", "mega constellation"],
    "Space Exploration": [
        "space exploration", "moon landing", "mars landing", "deep space",
        "asteroid mining", "space tourism", "orbital debris removal"
    ],

    # --- Cross-Disciplinary ---
    "Extended Reality (XR/AR/VR)": [
        "augmented reality", "ar", "virtual reality", "vr", "mixed reality", "mr", "metaverse", "spatial computing"
    ],
    "Defense Tech": [
        "defense tech", "military ai", "autonomous drone", "hypersonic", "cyber warfare"
    ],

    # --- Catch-All ---
    "Catch-All / Emerging Tech": [
        "emerging tech", "next-gen", "next generation", "future tech", "disruptive technology",
        "breakthrough innovation", "moonshot", "deep tech", "web3 social", "dao", "regenerative finance", "refi"
    ],
}


async def _run_sync_in_executor(func, *args, **kwargs):
    """Run synchronous function in executor to make it async."""
    loop = asyncio.get_running_loop()
    return await loop.run_in_executor(None, partial(func, *args, **kwargs))


def tag_description_to_themes(description: str, keywords_dict: Dict[str, List[str]], threshold: int = 1) -> List[str]:
    """
    Tags a company description to themes using keyword matching.
    
    Args:
    - description: Text to scan (e.g., API response).
    - keywords_dict: THEME_KEYWORDS dict.
    - threshold: Min keyword hits to assign a theme.
    
    Returns:
    - List of matched themes, sorted by score (desc).
    """
    if not description or description == 'N/A':
        return []
        
    desc_lower = description.lower()
    theme_scores = defaultdict(int)
    
    for theme, keywords in keywords_dict.items():
        # Build regex pattern for the theme's keywords (word boundaries, OR'd)
        pattern = r'\b(?:' + '|'.join(re.escape(kw.lower()) for kw in keywords) + r')\b'
        matches = len(re.findall(pattern, desc_lower))
        if matches >= threshold:
            theme_scores[theme] = matches
    
    # Sort by score descending, return theme names
    sorted_themes = sorted(theme_scores.items(), key=lambda x: x[1], reverse=True)
    return [theme for theme, _ in sorted_themes]


async def fetch_company_description(symbol: str) -> Tuple[str, str, str, str]:
    """
    Fetch company description and basic info using yfinance.
    
    Returns:
    - Tuple of (description, sector, industry, market_cap)
    """
    try:
        ticker_data = await _run_sync_in_executor(yf.Ticker, symbol, session=session)
        info = await _run_sync_in_executor(lambda: ticker_data.info)
        
        description = info.get('longBusinessSummary', 'N/A')
        sector = info.get('sector', 'N/A')
        industry = info.get('industry', 'N/A')
        market_cap_raw = info.get('marketCap', 0)
        
        # Format market cap
        if market_cap_raw and market_cap_raw > 0:
            if market_cap_raw >= 1e12:
                market_cap = f"${market_cap_raw/1e12:.1f}T"
            elif market_cap_raw >= 1e9:
                market_cap = f"${market_cap_raw/1e9:.1f}B"
            elif market_cap_raw >= 1e6:
                market_cap = f"${market_cap_raw/1e6:.1f}M"
            else:
                market_cap = f"${market_cap_raw:,.0f}"
        else:
            market_cap = 'N/A'
            
        return description, sector, industry, market_cap
        
    except Exception as e:
        print(f"Error fetching data for {symbol}: {e}")
        return 'N/A', 'N/A', 'N/A', 'N/A'


async def batch_tag_tickers(tickers: List[str]) -> pd.DataFrame:
    """
    Batch process tickers to tag them with themes.
    
    Args:
    - tickers: List of symbols, e.g., ['PONY', 'TSLA', 'UBER'].
    
    Returns:
    - DataFrame with columns: Symbol, Description, Sector, Industry, Market_Cap, Themes
    """
    results = []
    
    print(f"Processing {len(tickers)} tickers...")
    
    for i, ticker in enumerate(tickers, 1):
        print(f"[{i}/{len(tickers)}] Processing {ticker}...")
        
        try:
            description, sector, industry, market_cap = await fetch_company_description(ticker)
            themes = tag_description_to_themes(description, THEME_KEYWORDS)
            
            # If no themes found, assign catch-all
            if not themes:
                themes = ["Catch-All / Emerging Tech"]
            
            results.append({
                'Symbol': ticker,
                'Description': description[:200] + '...' if len(description) > 200 else description,
                'Sector': sector,
                'Industry': industry,
                'Market_Cap': market_cap,
                'Themes': ', '.join(themes),
                'Primary_Theme': themes[0] if themes else "Catch-All / Emerging Tech"
            })
            
        except Exception as e:
            print(f"Error processing {ticker}: {e}")
            results.append({
                'Symbol': ticker,
                'Description': 'Error fetching data',
                'Sector': 'N/A',
                'Industry': 'N/A',
                'Market_Cap': 'N/A',
                'Themes': "Catch-All / Emerging Tech",
                'Primary_Theme': "Catch-All / Emerging Tech"
            })
        
        # Small delay to be respectful to APIs
        await asyncio.sleep(0.1)
    
    return pd.DataFrame(results)


def load_ticker_list(file_path: str = 'src/data/liked_tickers.txt') -> List[str]:
    """Load ticker list from file."""
    try:
        with open(file_path, 'r') as f:
            content = f.read().strip()
            # Split by comma and clean up whitespace
            tickers = [ticker.strip().upper() for ticker in content.split(',') if ticker.strip()]
            return tickers
    except FileNotFoundError:
        print(f"Error: {file_path} not found.")
        return []
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return []


async def main():
    """Main function to run the theme tagging."""
    print("=== Stock Theme Tagger ===\n")
    
    # Load ticker list
    tickers = load_ticker_list()
    if not tickers:
        print("No tickers found. Exiting.")
        return
    
    print(f"Loaded {len(tickers)} tickers: {', '.join(tickers[:10])}{'...' if len(tickers) > 10 else ''}\n")
    
    # Process tickers
    df = await batch_tag_tickers(tickers)
    
    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"src/data/ticker_themes_{timestamp}.csv"
    df.to_csv(output_file, index=False)
    
    print(f"\n=== Results saved to {output_file} ===")
    
    # Display summary
    print("\n=== Theme Summary ===")
    theme_counts = df['Primary_Theme'].value_counts()
    for theme, count in theme_counts.items():
        print(f"{theme}: {count} tickers")
    
    # Display sample results
    print(f"\n=== Sample Results (first 10) ===")
    display_df = df[['Symbol', 'Sector', 'Primary_Theme', 'Market_Cap']].head(10)
    print(display_df.to_string(index=False))
    
    return df


if __name__ == "__main__":
    asyncio.run(main())
