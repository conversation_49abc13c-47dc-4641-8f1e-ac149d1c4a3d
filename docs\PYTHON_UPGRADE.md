# Python Upgrade to 3.11

This document outlines the process of upgrading the project from Python 3.8.5 to Python 3.11.

## Upgrade Process

1. Created a new virtual environment with Python 3.11:
   ```
   py -3.11 -m venv pythonicfin_env_py311
   ```

2. Installed dependencies in the new environment:
   ```
   .\pythonicfin_env_py311\Scripts\python.exe -m pip install --upgrade pip
   .\pythonicfin_env_py311\Scripts\python.exe -m pip install -r requirements_core.txt
   .\pythonicfin_env_py311\Scripts\python.exe -m pip install yfinance==0.2.55
   .\pythonicfin_env_py311\Scripts\python.exe -m pip install ruamel.yaml
   .\pythonicfin_env_py311\Scripts\python.exe -m pip install tabulate
   .\pythonicfin_env_py311\Scripts\python.exe -m pip install pandas_market_calendars
   ```

3. Fixed compatibility issues:
   - Fixed pandas_ta library issue with numpy NaN import
   - Fixed FutureWarning in display.py about downcasting behavior in replace
   - Fixed FutureWarning in scrapper.py about passing literal HTML to read_html
   - Fixed datetime.utcnow() deprecation warning
   - Updated yfinance news retrieval to work with newer versions

4. Updated batch files to use the new Python environment:
   - ticker.bat
   - screener.bat
   - enhanced_analysis.bat
   - Run_Python_Script.bat

## Benefits of Python 3.11

- Improved performance: Python 3.11 is significantly faster than Python 3.8
- Better error messages: More precise error locations and better error messages
- Improved type hints: Better support for type annotations
- New features: New string methods, improved asyncio, etc.

## Known Issues

- TA-Lib needs to be installed separately using a wheel file for Python 3.11:
  1. Use the wheel file: `ta_lib-0.6.3-cp311-cp311-win_amd64.whl`
  2. Install it using pip: `pip install ta_lib-0.6.3-cp311-cp311-win_amd64.whl`
- Some libraries may still have compatibility issues with Python 3.11
- If you encounter any issues, please report them

## Next Steps

- Test all functionality with the new Python version
- Consider upgrading dependencies to their latest versions compatible with Python 3.11
- Update documentation to reflect the new Python version requirement
