#!/usr/bin/env python3
"""
Test the signal scoring fix with real data structure
"""

import sys
sys.path.append('src')

import pandas as pd
from fidelity.chart_analysis.analysis import calculate_signal_score, get_signal_strength

def test_signal_with_real_data():
    """Test signal scoring with data that mimics real getStockDataV3 output"""

    print("=== TESTING SIGNAL SCORING FIX ===\n")

    # Create sample row data (from screener)
    sample_row = pd.Series({
        'Symbol': 'CIFR',
        'Company Name': 'Cipher Mining Inc',
        'Security Price': 12.28,
        '$Volume': 504.12,
        'EPS Growth (TTM vs Prior TTM)': -24.86,
        'EPS Growth (Last Qtr vs. Same Qtr Prior Yr)': -140,
        'EPS Growth (Proj Next Yr vs. This Yr)': 89.02,
        'Revenue Growth (TTM vs. Prior TTM)': 0.11,
        'Equity Summary Score (ESS) from LSEG StarMine': 5.2,
        'RS_Rating': 98,
        'Price Performance (4 Weeks)': 100.85,
        'Price Performance (13 Weeks)': 211.02,
        'Price Performance (52 Weeks)': 308.62,
        'MOMO': True
    })

    # Create sample technical data (from getStockDataV3)
    # This mimics the actual structure with potential Series values
    tech_data = pd.DataFrame({
        'Close': [12.28],
        '50': [10.5],
        '100': [9.8],
        '150': [9.2],
        '200': [8.5],
        'TTM': [True],
        'slope_50': [0.15],
        'slope_200': [0.08],
        'UDRatio': [0.85],
        '$VolumeM': [45.2],
        'RS_Rating': [98]
    })

    print(f"Testing signal calculation for {sample_row['Symbol']}...")
    print(f"Technical data shape: {tech_data.shape}")
    print(f"Technical data columns: {list(tech_data.columns)}")

    try:
        # Test the signal calculation
        signal_score = calculate_signal_score(sample_row, tech_data)
        signal_strength = get_signal_strength(signal_score)

        print(f"\n✅ SUCCESS!")
        print(f"Signal Score: {signal_score:.1f}/100")
        print(f"Signal Strength: {signal_strength}")

        # Test with None data
        print(f"\nTesting with None data...")
        signal_score_none = calculate_signal_score(sample_row, None)
        signal_strength_none = get_signal_strength(signal_score_none)
        print(f"Signal Score (None data): {signal_score_none:.1f}/100")
        print(f"Signal Strength (None data): {signal_strength_none}")

        # Test with empty DataFrame
        print(f"\nTesting with empty DataFrame...")
        empty_df = pd.DataFrame()
        signal_score_empty = calculate_signal_score(sample_row, empty_df)
        signal_strength_empty = get_signal_strength(signal_score_empty)
        print(f"Signal Score (empty data): {signal_score_empty:.1f}/100")
        print(f"Signal Strength (empty data): {signal_strength_empty}")

        print(f"\n🎉 All tests passed! The fix is working correctly.")

        # Test edge cases for defensive coding
        print(f"\n=== TESTING EDGE CASES ===")

        # Test with missing columns
        incomplete_df = pd.DataFrame({'Close': [12.28]})  # Only Close column
        signal_incomplete = calculate_signal_score(sample_row, incomplete_df)
        print(f"Incomplete data test: {signal_incomplete:.1f}/100")

        # Test with NaN values
        nan_df = pd.DataFrame({
            'Close': [float('nan')],
            '50': [10.5],
            'TTM': [None],
            'slope_50': [float('nan')]
        })
        signal_nan = calculate_signal_score(sample_row, nan_df)
        print(f"NaN data test: {signal_nan:.1f}/100")

        # Test with row missing attributes
        minimal_row = pd.Series({'Symbol': 'TEST'})
        signal_minimal = calculate_signal_score(minimal_row, tech_data)
        print(f"Minimal row test: {signal_minimal:.1f}/100")

        print(f"✅ All edge case tests passed!")

    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_signal_with_real_data()
