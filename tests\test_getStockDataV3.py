"""
Test script for getStockDataV3 function with the latest yfinance version.
This script tests if the function can successfully retrieve stock data.
"""

import sys
import os
import traceback
import asyncio
import aiohttp

# Add src directory to Python path
sys.path.append('src')

from stockdata.data_source import getStockDataV3

async def test_getStockDataV3():
    """Test the getStockDataV3 function with a known ticker."""
    print("Testing getStockDataV3 with ticker 'EBAY'...")

    try:
        # Get stock data for Apple
        async with aiohttp.ClientSession() as session:
            df = await getStockDataV3(session, 'META')

        if df is not None and not df.empty:
            print("Success! Retrieved data for :")
            print(f"Shape: {df.shape}")
            print(f"Date range: {df.index.min()} to {df.index.max()}")
            print(f"Columns: {df.columns.tolist()}")
            print("\nFirst 5 rows:")
            print(df.head())

            # Check if the new SD move columns are present
            required_columns = ['SD_Move_30D', 'SD_Move_60D', 'SD_Move_90D','RS_Rating','inside_day','TTM']
            if all(col in df.columns for col in required_columns):
                print("\nSuccess! SD Move columns are present.")
                print(df[required_columns].head())
                return True
            else:
                print("\nFailed! SD Move columns are missing.")
                return False
        else:
            print("Failed to retrieve data for UBER")
            return False
    except Exception as e:
        print(f"Error occurred: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_getStockDataV3())
