#!/usr/bin/env python3
"""
Test script to verify that TTM values are preserved as True/False instead of being converted to 0/1.
"""

import pandas as pd
import sys
import os
import asyncio
import aiohttp

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from fidelity.chart_analysis.analysis import calculate_signal_score, get_signal_strength
from stockdata.data_source import getStockDataV3

async def test_ttm_preservation():
    """Test that TTM values are preserved as boolean True/False"""
    
    print("=== TESTING TTM VALUE PRESERVATION ===\n")
    
    # Test with real AAOI data
    print("Fetching real AAOI data to check TTM values...")
    
    async with aiohttp.ClientSession() as session:
        try:
            df = await getStockDataV3(session, 'AAOI')
            
            if df is None or df.empty:
                print("❌ Could not fetch AAOI data")
                return
                
            print(f"✅ Successfully fetched AAOI data")
            print(f"DataFrame shape: {df.shape}")
            
            # Check TTM column structure
            ttm_columns = [col for col in df.columns if 'TTM' in str(col)]
            print(f"TTM-related columns: {ttm_columns}")
            
            if ttm_columns:
                ttm_col = ttm_columns[0]
                ttm_raw_value = df[ttm_col].iloc[0]
                print(f"Raw TTM value: {ttm_raw_value} (type: {type(ttm_raw_value)})")
                
                # Test the safe_get_column_value function behavior
                mock_row = {
                    'Symbol': 'AAOI',
                    'MOMO': False,
                    '$VolumeM': 50.0
                }
                
                print("\nTesting signal calculation with TTM preservation:")
                score = calculate_signal_score(mock_row, df)
                strength = get_signal_strength(score)
                
                print(f"Signal Score: {score:.1f}")
                print(f"Signal Strength: {strength}")
                
                # Now test the TTM value extraction specifically
                print(f"\nTesting TTM value extraction:")
                
                # Simulate the safe_get_ttm_value function
                def test_safe_get_ttm_value():
                    if df is None or df.empty:
                        return None
                        
                    # Try direct column access first
                    if 'TTM' in df.columns:
                        raw_value = df['TTM'].iloc[0]
                        if pd.notna(raw_value):
                            return raw_value  # Keep original type (boolean)
                    
                    # Try multi-level column access
                    for col in df.columns:
                        if isinstance(col, tuple) and len(col) >= 1 and col[0] == 'TTM':
                            raw_value = df[col].iloc[0]
                            if pd.notna(raw_value):
                                return raw_value  # Keep original type (boolean)
                    
                    return None
                
                ttm_preserved = test_safe_get_ttm_value()
                print(f"TTM value (preserved): {ttm_preserved} (type: {type(ttm_preserved)})")
                
                if isinstance(ttm_preserved, bool):
                    print("✅ TTM value correctly preserved as boolean!")
                elif ttm_preserved in [0, 1, 0.0, 1.0]:
                    print(f"❌ TTM value converted to numeric: {ttm_preserved}")
                else:
                    print(f"⚠️ TTM value has unexpected type: {type(ttm_preserved)}")
                    
            else:
                print("❌ No TTM column found in data")
                
        except Exception as e:
            print(f"❌ Error: {e}")

def test_synthetic_ttm_data():
    """Test with synthetic data to ensure TTM boolean preservation"""
    
    print("\n=== TESTING SYNTHETIC TTM DATA ===\n")
    
    # Create test data with explicit boolean TTM values
    test_cases = [
        ("TTM True", True),
        ("TTM False", False),
        ("TTM 1", 1),
        ("TTM 0", 0),
        ("TTM 1.0", 1.0),
        ("TTM 0.0", 0.0)
    ]
    
    for case_name, ttm_value in test_cases:
        print(f"Testing {case_name}: {ttm_value} (type: {type(ttm_value)})")
        
        # Create DataFrame with TTM value
        data = {
            'Close': [100.0],
            '50': [95.0],
            '150': [85.0],
            '200': [80.0],
            'TTM': [ttm_value],
            'slope_50': [0.1],
            'slope_200': [0.1],
            'UDRatio': [0.8],
            '$VolumeM': [50.0],
            'RS_Rating': [85.0]
        }
        
        df = pd.DataFrame(data)
        mock_row = {'Symbol': 'TEST', 'MOMO': False, '$VolumeM': 50.0}
        
        try:
            score = calculate_signal_score(mock_row, df)
            
            # Check what the TTM value becomes in the screener
            def extract_ttm_for_screener():
                if df is None or df.empty:
                    return None
                    
                # Try direct column access first
                if 'TTM' in df.columns:
                    raw_value = df['TTM'].iloc[0]
                    if pd.notna(raw_value):
                        return raw_value  # Keep original type
                
                return None
            
            preserved_ttm = extract_ttm_for_screener()
            print(f"  Preserved TTM: {preserved_ttm} (type: {type(preserved_ttm)})")
            print(f"  Signal Score: {score:.1f}")
            
            # Check if boolean values are preserved
            if isinstance(ttm_value, bool) and isinstance(preserved_ttm, bool):
                print("  ✅ Boolean TTM value preserved correctly")
            elif ttm_value in [1, 1.0] and preserved_ttm in [1, 1.0, True]:
                print("  ✅ Numeric 1 handled correctly")
            elif ttm_value in [0, 0.0] and preserved_ttm in [0, 0.0, False]:
                print("  ✅ Numeric 0 handled correctly")
            else:
                print(f"  ⚠️ TTM conversion: {ttm_value} -> {preserved_ttm}")
                
        except Exception as e:
            print(f"  ❌ Error: {e}")
        
        print()

if __name__ == "__main__":
    # Test with real data
    asyncio.run(test_ttm_preservation())
    
    # Test with synthetic data
    test_synthetic_ttm_data()
    
    print("🎯 TTM PRESERVATION TEST COMPLETED!")
    print("\nExpected results:")
    print("• TTM values should remain as True/False in the screener")
    print("• Boolean types should be preserved, not converted to 0/1")
    print("• Signal calculation should work correctly with boolean TTM")
    print("• No type conversion errors should occur")
