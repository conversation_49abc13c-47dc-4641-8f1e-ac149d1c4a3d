import os
import sys
import glob
import pandas as pd
import asyncio
import re

# Add the src directory to the Python path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from stockdata.data_source import getStockDataV3
from common.http_session import get_session

def to_yahoo_symbol(sym: str) -> str:
    s = (sym or "").strip().upper().replace("/", "-")
    m = re.fullmatch(r"([A-Z0-9]+)\.([A-Z])", s)
    return f"{m.group(1)}-{m.group(2)}" if m else s

async def main():
    screener_dir = 'src/data/screener/'
    list_of_files = glob.glob(os.path.join(screener_dir, 'filtered_sorted_*.xlsx'))
    if not list_of_files:
        print("No screener files found.")
        return

    # Collect & normalize symbols from all sheets
    all_symbols = set()
    for file in list_of_files:
        print(f"Processing file: {file}")
        df = pd.read_excel(file)
        # Robust column access
        col = next((c for c in df.columns if c.strip().lower() == "symbol"), None)
        if col is None:
            print(f"Warning: no 'Symbol' column in {file}; skipping.")
            continue
        all_symbols.update(to_yahoo_symbol(s) for s in df[col].astype(str).tolist())

    unique_symbols = sorted(all_symbols)
    print(f"\nFound {len(unique_symbols)} unique symbols to scan.")

    inside_day_symbols = []
    session = get_session('src/certs/my_ca_bundle.pem')

    # Process symbols sequentially
    for symbol in unique_symbols:
        try:
            stock_df = await getStockDataV3(session, symbol)
            if stock_df['inside_day'].iloc[0]:
                print("Inside day found for:", symbol)
                inside_day_symbols.append(symbol)

        except Exception as e:
            print(f"Skipping {symbol} due to an error: {e}")

    session.close()

    # Print the comma-separated list of symbols
    if inside_day_symbols:
        print("\nSymbols with inside days:")
        print(','.join(inside_day_symbols))
    else:
        print("\nNo symbols with inside days found.")

if __name__ == "__main__":
    asyncio.run(main())
