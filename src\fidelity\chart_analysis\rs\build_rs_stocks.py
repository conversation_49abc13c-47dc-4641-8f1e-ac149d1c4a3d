# file: build_rs_stocks.py
import os
import re
import glob
import math
import logging
from itertools import islice
from datetime import datetime
import numpy as np
import pandas as pd
import yfinance as yf
import sys

# Add src directory to Python path
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))
from universe_from_symbol_dir import load_stocks_only_from_symbol_dir

# =========================
# Config
# =========================
UNIVERSE_SOURCE   = "nasdaq"   # "screener" or "nasdaq"
OUTPUT_DIR        = "output"
CACHE_DIR         = os.path.join(OUTPUT_DIR, "cache")
RESUME            = True       # enable checkpoint/resume
BENCH             = "^GSPC"    # or "SPY"
PRICE_PERIOD      = "2y"
PRICE_INTERVAL    = "1d"
BATCH_SIZE        = 250
MIN_PRICE         = 8.0        # filter out stocks below this price
AVG_VOLUME_DAYS   = 50
MIN_AVG_DOLLAR_VOLUME = 10_000_000
WRITE_THRESHOLDS  = True       # also write 7 cutoffs like RSRATING.csv
WINSORIZE_RESULTS = True       # enable winsorization step
WINSORIZE_LOW     = 0.01       # lower quantile cap for winsorization
WINSORIZE_HIGH    = 0.99       # upper quantile cap for winsorization


# Only used if UNIVERSE_SOURCE="screener"
SCREENER_LOCATIONS = [
    os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data', 'screener'),
    'src/data/screener',
    '.',
]

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# =========================
# Helpers
# =========================
def chunked(iterable, n):
    it = iter(iterable)
    while True:
        batch = list(islice(it, n))
        if not batch:
            return
        yield batch

def looks_like_symbol(s: str) -> bool:
    if not isinstance(s, str):
        return False
    s = s.strip().upper()
    if not s or " " in s or len(s) > 10:
        return False
    if s.startswith(("AS OF ", "FIDELITY ", "ALL DATA ", "HIGHLIGHTED ", "MEMBER ",
                     "PERFORMANCE DATA ", "SMITHFIELD ", "DATA AND INFORMATION ",
                     "SO THAT AN INVESTOR")):
        return False
    return re.fullmatch(r"[A-Z0-9./-]+", s) is not None

def to_yahoo_symbol(sym: str) -> str:
    s = sym.strip().upper().replace("/", "-")
    m = re.fullmatch(r"([A-Z0-9]+)\.([A-Z])", s)
    if m:
        s = f"{m.group(1)}-{m.group(2)}"
    return s

def get_close_from_download(df, ticker):
    try:
        return df[ticker]["Close"].dropna()
    except Exception:
        if isinstance(df, pd.DataFrame) and "Close" in df.columns:
            return df["Close"].dropna()
        return pd.Series(dtype=float)

def get_volume_from_download(df, ticker):
    try:
        return df[ticker]["Volume"].dropna()
    except Exception:
        if isinstance(df, pd.DataFrame) and "Volume" in df.columns:
            return df["Volume"].dropna()
        return pd.Series(dtype=float)

# =========================
# Core calc helpers
# =========================
def weighted_rs_score(stock_close: pd.Series, ref_close: pd.Series) -> float | None:
    periods = [63, 126, 189, 252]
    if len(stock_close) < max(periods)+1 or len(ref_close) < max(periods)+1:
        return None
    w = [0.4, 0.2, 0.2, 0.2]
    sp = [stock_close.iloc[-1] / stock_close.iloc[-n] for n in periods]
    rp = [ref_close.iloc[-1]   / ref_close.iloc[-n]   for n in periods]
    rs_stock = sum(p * ww for p, ww in zip(sp, w))
    rs_ref   = sum(p * ww for p, ww in zip(rp, w))
    return float((rs_stock / rs_ref) * 100.0)

def weighted_rs_score_fallback(stock_close: pd.Series, ref_close: pd.Series) -> float | None:
    """
    Fallback RS calculation for newer stocks with limited history.
    Uses available data with adaptive periods, minimum 120 days required.
    """
    min_required_days = 120  # Minimum ~6 months of data
    if len(stock_close) < min_required_days or len(ref_close) < min_required_days:
        return None

    # Use adaptive periods based on available data
    max_available = min(len(stock_close), len(ref_close)) - 1

    # Define periods as fractions of available data, but cap at standard periods
    base_periods = [63, 126, 189, 252]
    periods = []
    for period in base_periods:
        if period <= max_available:
            periods.append(period)
        else:
            # Use fraction of available data for missing periods
            fraction_period = int(max_available * (period / 252))
            if fraction_period >= 21:  # At least 1 month
                periods.append(fraction_period)

    if len(periods) == 0:
        return None

    # Adjust weights based on available periods
    if len(periods) == 4:
        w = [0.4, 0.2, 0.2, 0.2]
    elif len(periods) == 3:
        w = [0.5, 0.25, 0.25]
    elif len(periods) == 2:
        w = [0.6, 0.4]
    else:  # len(periods) == 1
        w = [1.0]

    sp = [stock_close.iloc[-1] / stock_close.iloc[-n] for n in periods]
    rp = [ref_close.iloc[-1]   / ref_close.iloc[-n]   for n in periods]
    rs_stock = sum(p * ww for p, ww in zip(sp, w))
    rs_ref   = sum(p * ww for p, ww in zip(rp, w))
    return float((rs_stock / rs_ref) * 100.0)

def percentile_rank_1_99(values: np.ndarray, x: float) -> int:
    if len(values) < 2 or not np.isfinite(x):
        return 1
    pct = np.sum(values < x) / (len(values) - 1)
    return int(np.clip(1 + 98 * pct, 1, 99))


def normalize_and_winsorize_results(in_path: str, out_dir: str):
    """
    Winsorize RS, recompute ranks/percentiles, and write new thresholds.
    """
    if not WINSORIZE_RESULTS:
        logging.info("Skipping winsorization step.")
        return

    logging.info(f"Starting winsorization for {in_path}...")
    df = pd.read_csv(in_path)

    # --- Winsorize ---
    rs = df["Relative Strength"].astype(float)
    lo = rs.quantile(WINSORIZE_LOW)
    hi = rs.quantile(WINSORIZE_HIGH)
    df["Relative Strength"] = rs.clip(lo, hi)

    # --- Recompute Percentiles & Rank ---
    values = df["Relative Strength"].to_numpy(dtype=float)
    df["Percentile"] = [percentile_rank_1_99(values, x) for x in values]
    df = df.sort_values(["Percentile", "Relative Strength"], ascending=[False, False]).reset_index(drop=True)
    df["Rank"] = np.arange(1, len(df) + 1)

    # --- Save Winsorized Table ---
    ordered_cols = ["Rank", "Ticker", "Exchange", "Relative Strength", "Percentile"]
    cols = [c for c in ordered_cols if c in df.columns] + [c for c in df.columns if c not in ordered_cols]
    df = df[cols]
    out_path = os.path.join(out_dir, "rs_stocks_winsorized.csv")
    df.to_csv(out_path, index=False)
    logging.info(f"Wrote winsorized table to {out_path}")

    # --- Compute & Write Winsorized Thresholds ---
    percentiles = [98, 90, 70, 50, 30, 10, 2]
    thresholds = np.percentile(values, percentiles).tolist()
    thresholds = [round(float(x), 2) for x in thresholds]
    ts = datetime.utcnow().strftime("%Y%m%dT")
    header = ["timestamp", "P98", "P90", "P70", "P50", "P30", "P10", "P02", "CapLow", "CapHigh"]
    row = [ts] + thresholds + [round(float(lo), 2), round(float(hi), 2)]
    rating_out = os.path.join(out_dir, "RSRATING_winsor.csv")
    if not os.path.exists(rating_out):
        pd.DataFrame([row], columns=header).to_csv(rating_out, index=False)
    else:
        pd.DataFrame([row]).to_csv(rating_out, mode="a", index=False, header=False)
    logging.info(f"Wrote winsorized thresholds to {rating_out}")


# =========================
# Checkpointing
# =========================
def cache_path_for_date(market_date: str) -> str:
    os.makedirs(CACHE_DIR, exist_ok=True)
    return os.path.join(CACHE_DIR, f"rs_scores_{market_date}.csv")

def load_scores_cache(cache_path: str) -> dict[str, float]:
    if not os.path.exists(cache_path):
        return {}
    df = pd.read_csv(cache_path)
    if "Ticker" not in df.columns or "Score" not in df.columns:
        return {}
    return dict(zip(df["Ticker"].astype(str), df["Score"].astype(float)))

def append_scores_cache(cache_path: str, batch_scores: dict[str, float]):
    if not batch_scores:
        return
    df = pd.DataFrame(
        {"Ticker": list(batch_scores.keys()), "Score": list(batch_scores.values())}
    )
    # Append (header if file missing)
    header = not os.path.exists(cache_path)
    df.to_csv(cache_path, mode="a", index=False, header=header)

# =========================
# Batch scoring
# =========================
def rs_scores_for_today(symbols, bench_close: pd.Series, skip: set[str], cache_path: str | None) -> dict[str, float]:
    """
    Compute today's RS scores for symbols not in 'skip'; append each batch to cache_path if provided.
    """
    scores = {}
    todo = [s for s in symbols if s not in skip]
    total = len(todo)
    done = 0
    if total == 0:
        logging.info("No symbols to compute (all present in cache).")
        return scores

    for batch in chunked(todo, BATCH_SIZE):
        df = yf.download(
            tickers=batch,
            period=PRICE_PERIOD,
            interval=PRICE_INTERVAL,
            auto_adjust=True,
            group_by="ticker",
            threads=True,
            progress=False,
        )
        batch_scores = {}
        for sym in batch:
            sc = get_close_from_download(df, sym)
            if sc.empty or (MIN_PRICE > 0 and sc.iloc[-1] < MIN_PRICE):
                continue

            sv = get_volume_from_download(df, sym)
            if sv.empty:
                continue

            # Align series by index
            aligned_close, aligned_volume = sc.align(sv, join='inner')
            if len(aligned_close) < AVG_VOLUME_DAYS:
                continue

            dollar_volume = aligned_close * aligned_volume
            avg_dollar_volume = dollar_volume.rolling(window=AVG_VOLUME_DAYS).mean().iloc[-1]

            if avg_dollar_volume < MIN_AVG_DOLLAR_VOLUME:
                continue

            score = weighted_rs_score(sc, bench_close)
            if score is None:
                # Try fallback calculation for newer stocks
                score = weighted_rs_score_fallback(sc, bench_close)
            if score is not None and math.isfinite(score):
                batch_scores[sym] = score
        del df

        # Append this batch to cache immediately
        if cache_path:
            append_scores_cache(cache_path, batch_scores)

        scores.update(batch_scores)
        done += len(batch)
        logging.info(f"RS scores progress: {done}/{total} (newly computed)")
    return scores

# =========================
# Universe loaders
# =========================
def load_universe_from_screener() -> pd.DataFrame:
    files = []
    for base in SCREENER_LOCATIONS:
        if os.path.exists(base):
            files = glob.glob(os.path.join(base, "screener_results*.xls"))
            if files:
                break
    if not files:
        files = glob.glob("screener_results*.xls")
    if not files:
        raise FileNotFoundError("No screener_results*.xls files found in known locations.")

    logging.info(f"Found {len(files)} screener files.")
    frames = []
    for p in files:
        logging.info(f"Reading screener file: {p}")
        frames.append(pd.read_excel(p))

    df_raw = pd.concat(frames, ignore_index=True)
    for col in ["Symbol", "Exchange"]:
        if col not in df_raw.columns:
            df_raw[col] = ""

    df = df_raw[df_raw["Symbol"].apply(looks_like_symbol)].copy()
    df["Symbol"] = df["Symbol"].astype(str).str.strip().str.upper().map(to_yahoo_symbol)
    df = df.drop_duplicates(subset=["Symbol"]).reset_index(drop=True)
    return df[["Symbol", "Exchange"]]

# =========================
# Main
# =========================
def main():
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    logging.info(f"Starting RS stocks build (universe={UNIVERSE_SOURCE}, resume={RESUME}).")

    # 1) Universe
    if UNIVERSE_SOURCE.lower() == "screener":
        universe_df = load_universe_from_screener()
    elif UNIVERSE_SOURCE.lower() == "nasdaq":
        # load_stocks_only_from_symbol_dir returns ["Symbol","Exchange","SecurityName"]
        universe_df = load_stocks_only_from_symbol_dir()
        if "Exchange" not in universe_df.columns:
            universe_df["Exchange"] = ""
    else:
        raise ValueError("UNIVERSE_SOURCE must be 'screener' or 'nasdaq'.")
    universe_df = universe_df[["Symbol", "Exchange"]].copy()

    symbols = [s for s in universe_df["Symbol"].astype(str) if s]
    logging.info(f"Universe size (cleaned): {len(symbols)} symbols.")

    # 2) Benchmark
    logging.info("Downloading benchmark series…")
    bench_df = yf.download([BENCH], period=PRICE_PERIOD, interval=PRICE_INTERVAL,
                           auto_adjust=True, group_by="ticker", progress=False)
    bench_close = get_close_from_download(bench_df, BENCH).dropna()
    if len(bench_close) < 252:
        raise RuntimeError("Not enough history in benchmark; increase PRICE_PERIOD.")
    market_date = bench_close.index[-1].strftime("%Y-%m-%d")
    logging.info(f"Market date detected: {market_date}")

    # 3) Load/compute today's scores with resume
    cache_file = cache_path_for_date(market_date) if RESUME else None
    cached_scores = load_scores_cache(cache_file) if cache_file else {}
    # Keep only cached tickers that are in today's universe
    cached_scores = {k: v for k, v in cached_scores.items() if k in set(symbols)}
    logging.info(f"Loaded {len(cached_scores)} scores from cache for {market_date}.")

    new_scores = rs_scores_for_today(symbols, bench_close, skip=set(cached_scores.keys()), cache_path=cache_file)
    today_scores = {**cached_scores, **new_scores}
    logging.info(f"Total RS scores available today: {len(today_scores)}")

    if not today_scores:
        raise RuntimeError("No RS scores computed; check connectivity or symbol cleaning.")

    today_vals = np.array(list(today_scores.values()), dtype=float)

    # 4) Assemble output
    logging.info("Assembling output table…")
    exch_map = dict(zip(universe_df["Symbol"], universe_df["Exchange"]))
    rows = []
    for sym, score in today_scores.items():
        rows.append({
            "Ticker": sym,
            "Exchange": exch_map.get(sym, ""),
            "Relative Strength": round(score, 2),
            "Percentile": percentile_rank_1_99(today_vals, score),
        })

    out = pd.DataFrame(rows).sort_values(
        by=["Percentile","Relative Strength"], ascending=[False, False]
    ).reset_index(drop=True)
    out.index = out.index + 1
    out.insert(0, "Rank", out.index)

    # 5) Save rs_stocks.csv
    out = out[["Rank", "Ticker", "Exchange", "Relative Strength", "Percentile"]]
    out_path = os.path.join(OUTPUT_DIR, "rs_stocks.csv")
    out.to_csv(out_path, index=False)
    logging.info(f"Wrote {out_path} with {len(out)} rows on {datetime.now():%Y-%m-%d}")
    print(f"Wrote {out_path} with {len(out)} rows on {datetime.now():%Y-%m-%d}")

    # 6) (Optional) thresholds like RSRATING.csv
    if WRITE_THRESHOLDS:
        thresholds = sorted(np.percentile(today_vals, [98,90,70,50,30,10,2]).tolist(), reverse=True)
        ts = datetime.utcnow().strftime("%Y%m%dT")
        rsrating_path = os.path.join(OUTPUT_DIR, "RSRATING.csv")
        header = ["timestamp","P98","P90","P70","P50","P30","P10","P02"]
        row = [ts] + [round(x, 2) for x in thresholds]
        if not os.path.exists(rsrating_path):
            pd.DataFrame([row], columns=header).to_csv(rsrating_path, index=False)
        else:
            pd.DataFrame([row]).to_csv(rsrating_path, mode="a", index=False, header=False)
        logging.info(f"Wrote thresholds to {rsrating_path}: {thresholds}")

    # 7) (Optional) Winsorize results
    normalize_and_winsorize_results(in_path=out_path, out_dir=OUTPUT_DIR)


if __name__ == "__main__":
    main()
