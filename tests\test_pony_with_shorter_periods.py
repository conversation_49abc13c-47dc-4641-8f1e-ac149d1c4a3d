#!/usr/bin/env python3
"""
Test PONY RS calculation with shorter periods to accommodate newer IPOs
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

import pandas as pd
import yfinance as yf
import numpy as np
from datetime import datetime, timed<PERSON>ta

def weighted_rs_score_short_periods(stock_close: pd.Series, ref_close: pd.Series) -> float | None:
    """
    Modified RS calculation with shorter periods for newer stocks
    Original: [63, 126, 189, 252] (needs 253 days)
    Modified: [42, 84, 126, 168] (needs 169 days) - roughly 2, 4, 6, 8 months
    """
    periods = [42, 84, 126, 168]  # Shorter periods for newer stocks
    if len(stock_close) < max(periods)+1 or len(ref_close) < max(periods)+1:
        return None
    w = [0.4, 0.2, 0.2, 0.2]
    sp = [stock_close.iloc[-1] / stock_close.iloc[-n] for n in periods]
    rp = [ref_close.iloc[-1]   / ref_close.iloc[-n]   for n in periods]
    rs_stock = sum(p * ww for p, ww in zip(sp, w))
    rs_ref   = sum(p * ww for p, ww in zip(rp, w))
    return float((rs_stock / rs_ref) * 100.0)

def test_pony_with_short_periods():
    """Test PONY RS calculation with shorter periods"""
    print("=== Testing PONY with Shorter RS Periods ===")
    
    try:
        # Get PONY data
        print("Getting PONY data...")
        pony = yf.Ticker("PONY")
        end_date = datetime.now()
        start_date = end_date - timedelta(days=365)
        pony_hist = pony.history(start=start_date, end=end_date)
        
        print(f"PONY data: {len(pony_hist)} days")
        
        # Get benchmark data (SPY)
        print("Getting benchmark data...")
        spy = yf.Ticker("SPY")
        spy_hist = spy.history(start=start_date, end=end_date)
        
        print(f"SPY data: {len(spy_hist)} days")
        
        # Test original periods (should fail)
        print("\n--- Original Periods Test ---")
        original_periods = [63, 126, 189, 252]
        min_days_original = max(original_periods) + 1
        print(f"Original periods: {original_periods}")
        print(f"Minimum days required: {min_days_original}")
        print(f"PONY has {len(pony_hist)} days: {'✓ PASS' if len(pony_hist) >= min_days_original else '❌ FAIL'}")
        
        # Test shorter periods (should pass)
        print("\n--- Shorter Periods Test ---")
        short_periods = [42, 84, 126, 168]
        min_days_short = max(short_periods) + 1
        print(f"Shorter periods: {short_periods}")
        print(f"Minimum days required: {min_days_short}")
        print(f"PONY has {len(pony_hist)} days: {'✓ PASS' if len(pony_hist) >= min_days_short else '❌ FAIL'}")
        
        # Calculate RS with shorter periods
        if len(pony_hist) >= min_days_short and len(spy_hist) >= min_days_short:
            print("\n--- RS Calculation ---")
            
            # Align dates
            common_dates = pony_hist.index.intersection(spy_hist.index)
            pony_close = pony_hist.loc[common_dates, 'Close']
            spy_close = spy_hist.loc[common_dates, 'Close']
            
            print(f"Common trading days: {len(common_dates)}")
            
            rs_score = weighted_rs_score_short_periods(pony_close, spy_close)
            
            if rs_score is not None:
                print(f"✓ PONY RS Score (short periods): {rs_score:.2f}")
                
                # Calculate percentile (rough estimate)
                if rs_score > 100:
                    percentile = min(99, 50 + (rs_score - 100) / 10)
                else:
                    percentile = max(1, 50 - (100 - rs_score) / 10)
                
                print(f"  Estimated percentile: {percentile:.0f}")
                return True
            else:
                print("❌ RS calculation returned None")
                return False
        else:
            print("❌ Not enough data even for shorter periods")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_other_adrs_with_short_periods():
    """Test other ADRs with shorter periods for comparison"""
    print("\n=== Testing Other ADRs with Shorter Periods ===")
    
    adrs = ['BABA', 'JD', 'BIDU', 'NIO', 'XPEV']
    
    # Get benchmark data
    spy = yf.Ticker("SPY")
    end_date = datetime.now()
    start_date = end_date - timedelta(days=365)
    spy_hist = spy.history(start=start_date, end=end_date)
    
    for symbol in adrs:
        print(f"\n--- {symbol} ---")
        try:
            ticker = yf.Ticker(symbol)
            hist = ticker.history(start=start_date, end=end_date)
            
            if len(hist) >= 169:  # min for short periods
                # Align dates
                common_dates = hist.index.intersection(spy_hist.index)
                stock_close = hist.loc[common_dates, 'Close']
                spy_close = spy_hist.loc[common_dates, 'Close']
                
                rs_score = weighted_rs_score_short_periods(stock_close, spy_close)
                
                if rs_score is not None:
                    print(f"  RS Score: {rs_score:.2f}")
                    print(f"  Data days: {len(hist)}")
                else:
                    print(f"  RS calculation failed")
            else:
                print(f"  Insufficient data: {len(hist)} days")
                
        except Exception as e:
            print(f"  Error: {e}")

if __name__ == "__main__":
    print("=" * 60)
    print("TESTING PONY WITH SHORTER RS PERIODS")
    print("=" * 60)
    
    success = test_pony_with_short_periods()
    test_other_adrs_with_short_periods()
    
    print("\n" + "=" * 60)
    print("SUMMARY")
    print("=" * 60)
    if success:
        print("✓ PONY can be calculated with shorter periods!")
        print("  Consider modifying the periods in build_rs_stocks.py")
        print("  from [63, 126, 189, 252] to [42, 84, 126, 168]")
        print("  This would allow stocks with 169+ days of data instead of 253+")
    else:
        print("❌ PONY still cannot be calculated even with shorter periods")
        print("  May need even shorter periods or different approach")
