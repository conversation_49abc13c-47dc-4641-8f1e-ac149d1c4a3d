@echo off

REM Change to repo root (this script assumed to be in repo root; adjust if needed)
cd /d "%~dp0"

REM Load .env variables if present
if exist .env (
  for /f "tokens=* delims=" %%a in (.env) do set %%a
)

REM Ensure PYTHONPATH includes src
set PYTHONPATH=%CD%\src

REM Optionally activate your venv if you have one (update path if different)
if exist pythonicfin_env_py311\Scripts\activate.bat (
  call pythonicfin_env_py311\Scripts\activate.bat
)

REM Launch the UI-only pipeline (no recompute). Requires pandas and your project deps.
python -m fidelity.chart_analysis.ui_only

pause

