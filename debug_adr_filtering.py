#!/usr/bin/env python3
"""
Debug script to trace where ADR stocks are being filtered out
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from fidelity.chart_analysis.rs.universe_from_symbol_dir import (
    _load_txt, _base_filters, NASDAQLISTED_URL, OTHERLISTED_URL, 
    CODE_MAP, ALLOWED_EXCHANGES, to_yahoo_symbol
)
import pandas as pd
import re

def debug_adr_filtering():
    """Step by step debugging of ADR filtering"""
    
    adr_stocks = ['BABA', 'PONY', 'JD', 'BIDU', 'NIO', 'XPEV']
    
    print("=== STEP 1: Load raw data ===")
    nas = _load_txt(NASDAQLISTED_URL)
    oth = _load_txt(OTHERLISTED_URL)
    
    print(f"NASDAQ file: {nas.shape}")
    print(f"OTHER file: {oth.shape}")
    
    # Check ADRs in raw data
    nas_adrs = nas[nas['Symbol'].isin(adr_stocks)]
    oth_adrs = oth[oth['ACT Symbol'].isin(adr_stocks)]
    
    print(f"ADRs in NASDAQ raw: {len(nas_adrs)} - {nas_adrs['Symbol'].tolist()}")
    print(f"ADRs in OTHER raw: {len(oth_adrs)} - {oth_adrs['ACT Symbol'].tolist()}")
    
    print("\n=== STEP 2: Financial Status Filter (NASDAQ only) ===")
    if "Financial Status" in nas.columns:
        non_normal = {'D', 'E', 'Q', 'G', 'H', 'J', 'K'}
        nas_filtered = nas[~nas["Financial Status"].str.upper().isin(non_normal)]
        nas_adrs_after_fin = nas_filtered[nas_filtered['Symbol'].isin(adr_stocks)]
        print(f"NASDAQ after financial filter: {nas_filtered.shape}")
        print(f"ADRs remaining in NASDAQ: {len(nas_adrs_after_fin)} - {nas_adrs_after_fin['Symbol'].tolist()}")
        nas = nas_filtered
    
    print("\n=== STEP 3: Base Filters (ETF, Test Issue, NextShares) ===")
    nas_base = _base_filters(nas).copy()
    oth_base = _base_filters(oth).copy()
    
    nas_adrs_after_base = nas_base[nas_base['Symbol'].isin(adr_stocks)]
    oth_adrs_after_base = oth_base[oth_base['ACT Symbol'].isin(adr_stocks)]
    
    print(f"NASDAQ after base filters: {nas_base.shape}")
    print(f"OTHER after base filters: {oth_base.shape}")
    print(f"ADRs remaining in NASDAQ: {len(nas_adrs_after_base)} - {nas_adrs_after_base['Symbol'].tolist()}")
    print(f"ADRs remaining in OTHER: {len(oth_adrs_after_base)} - {oth_adrs_after_base['ACT Symbol'].tolist()}")
    
    print("\n=== STEP 4: Symbol Column Mapping ===")
    # Handle OTHER file symbol mapping
    if "NASDAQ Symbol" in oth_base.columns:
        oth_base["Symbol"] = oth_base["NASDAQ Symbol"]
    elif "CQS Symbol" in oth_base.columns:
        oth_base["Symbol"] = oth_base["CQS Symbol"]
    elif "ACT Symbol" in oth_base.columns:
        oth_base["Symbol"] = oth_base["ACT Symbol"]
    
    oth_adrs_after_symbol = oth_base[oth_base['Symbol'].isin(adr_stocks)]
    print(f"ADRs in OTHER after symbol mapping: {len(oth_adrs_after_symbol)} - {oth_adrs_after_symbol['Symbol'].tolist()}")
    
    print("\n=== STEP 5: Exchange Mapping ===")
    nas_base["Exchange"] = "NASDAQ"
    if "Exchange" in oth_base.columns:
        oth_base["Exchange"] = oth_base["Exchange"].map(CODE_MAP).fillna("OTHER")
    else:
        oth_base["Exchange"] = "OTHER"
    
    print(f"Exchange mapping for OTHER ADRs:")
    if len(oth_adrs_after_symbol) > 0:
        print(oth_adrs_after_symbol[['Symbol', 'Exchange']].to_string())
    
    print("\n=== STEP 6: Combine DataFrames ===")
    df = pd.concat([nas_base[["Symbol","Security Name","Exchange"]],
                    oth_base[["Symbol","Security Name","Exchange"]]], ignore_index=True)
    
    combined_adrs = df[df['Symbol'].isin(adr_stocks)]
    print(f"Combined DataFrame: {df.shape}")
    print(f"ADRs after combining: {len(combined_adrs)} - {combined_adrs['Symbol'].tolist()}")
    
    print("\n=== STEP 7: Exchange Filter ===")
    print(f"ALLOWED_EXCHANGES: {ALLOWED_EXCHANGES}")
    df_exchange_filtered = df[df["Exchange"].isin(ALLOWED_EXCHANGES)]
    
    exchange_adrs = df_exchange_filtered[df_exchange_filtered['Symbol'].isin(adr_stocks)]
    print(f"After exchange filter: {df_exchange_filtered.shape}")
    print(f"ADRs after exchange filter: {len(exchange_adrs)} - {exchange_adrs['Symbol'].tolist()}")
    
    print("\n=== STEP 8: Symbol Cleaning ===")
    df_exchange_filtered["Symbol"] = df_exchange_filtered["Symbol"].astype(str).map(to_yahoo_symbol)
    df_clean = df_exchange_filtered[df_exchange_filtered["Symbol"] != ""].drop_duplicates(subset=["Symbol"]).copy()
    
    clean_adrs = df_clean[df_clean['Symbol'].isin(adr_stocks)]
    print(f"After symbol cleaning: {df_clean.shape}")
    print(f"ADRs after symbol cleaning: {len(clean_adrs)} - {clean_adrs['Symbol'].tolist()}")
    
    print("\n=== STEP 9: Security Name Filtering ===")
    name = df_clean["Security Name"].fillna("").str.upper()
    
    # EXCLUDE patterns
    EXCLUDE = (
        " PREFERRED", " PFD", " PREFERENCE",
        " WARRANT", " RIGHTS", " RIGHT ", " RT ", " RTS ",
        " UNIT", " UNITS",
        " NOTE", " NOTES", " BOND", " DEBENTURE",
        " TRUST", " WHEN ISSUED", " WI ",
        " DEPOSITARY SHARE",
        " EQUITY UNITS", " TRACKING STOCK",
        " OTC",
    )
    mask_excl = ~name.str.contains("|".join(map(re.escape, EXCLUDE)))
    
    # Check which ADRs are excluded
    adr_names = df_clean[df_clean['Symbol'].isin(adr_stocks)]['Security Name'].fillna("").str.upper()
    print(f"ADR security names:")
    for i, (symbol, sec_name) in enumerate(zip(df_clean[df_clean['Symbol'].isin(adr_stocks)]['Symbol'], adr_names)):
        excluded = not mask_excl.iloc[df_clean[df_clean['Symbol'] == symbol].index[0]]
        print(f"  {symbol}: {sec_name} - {'EXCLUDED' if excluded else 'INCLUDED'}")
    
    # KEEP patterns
    KEEP = ["COMMON STOCK", "ORDINARY SHARES"]
    KEEP += ["AMERICAN DEPOSITARY SHARES", " ADS "]  # ADR patterns
    
    mask_keep = name.str.contains("|".join(map(re.escape, KEEP)))
    
    print(f"\nKEEP patterns: {KEEP}")
    print(f"ADR names vs KEEP patterns:")
    for i, (symbol, sec_name) in enumerate(zip(df_clean[df_clean['Symbol'].isin(adr_stocks)]['Symbol'], adr_names)):
        kept = mask_keep.iloc[df_clean[df_clean['Symbol'] == symbol].index[0]]
        print(f"  {symbol}: {sec_name} - {'KEPT' if kept else 'NOT KEPT'}")
    
    print("\n=== FINAL RESULT ===")
    final_mask = mask_keep & mask_excl
    trimmed = df_clean[final_mask].copy()
    
    final_adrs = trimmed[trimmed['Symbol'].isin(adr_stocks)]
    print(f"Final DataFrame: {trimmed.shape}")
    print(f"Final ADRs: {len(final_adrs)} - {final_adrs['Symbol'].tolist()}")
    
    if len(final_adrs) > 0:
        print(final_adrs[['Symbol', 'Exchange', 'Security Name']].to_string())

if __name__ == "__main__":
    debug_adr_filtering()
