import pandas as pd
from util.rs_rating import RSCalculator # Corrected absolute import from root
from util.util import read_and_filter_csv # Corrected absolute import from root
import glob
from fidelity.chart_analysis.download import downloadChartsFidelityV2 # Corrected absolute import from root

def printFilteredData(tickers):

     stock_symbols = [entry for entry in tickers if isinstance(entry, str) and len(entry) <= 5]

     print(stock_symbols)

     print(len(stock_symbols))

def  groupAndFilter(df,filter):

    # Remove rows where 'Security Type' is 'Common Stock (REIT)'
    df = df[df['Security Type'] != 'Common Stock (REIT)']

    # Get the count of occurrences within each 'Sector'
    sector_counts = df['Sector'].value_counts().reset_index(name='Sector_Count')
    sector_counts.columns = ['Sector', 'Sector_Count']

    # Merge the count back into the original DataFrame
    df = pd.merge(df, sector_counts, on='Sector')


    if filter == 'Y':
       # not to miss 30% :)
       df_filtered = df[ (
                       ((df['EPS Growth (TTM vs Prior TTM)'] >= 15)  | (df['EPS Growth (Last Qtr vs. Same Qtr Prior Yr)'] >= 15))
                       #&
                       #((df['Revenue Growth (TTM vs. Prior TTM)'] >= 0) | (df['Revenue Growth (Last Qtr vs. Same Qtr Prior Yr)'] >= 0))
                       )]

       # not to miss revenue companies pre growth, lets say revenue > 30%

       df_filtered1 = df[(
                       ((df['EPS Growth (TTM vs Prior TTM)'].isna())  | (df['EPS Growth (Last Qtr vs. Same Qtr Prior Yr)'].isna()))
                       &
                       ((df['Revenue Growth (TTM vs. Prior TTM)'] >= 30) | (df['Revenue Growth (Last Qtr vs. Same Qtr Prior Yr)'] >= 30))
                       )]
       # consistent growth
       #EPS Growth (3 Year History)	EPS Growth (5 Year Historical)	EPS Growth (Proj Next Yr vs. This Yr)
       df_filtered2 = df[(df['EPS Growth (Proj Next Yr vs. This Yr)'] > 50)]

       df = pd.concat([df_filtered, df_filtered1,df_filtered2], ignore_index=True)


    # atleast covered 50% off 52 week lows
    # Calculate 52-week low price
    df["52_Week_Low"] = df["Security Price"] * (1 - df["% Above 52 Week Low"] / 100)

      # Filter based on the condition
    df = df[df["Security Price"] >= df["52_Week_Low"] * 1.5]

    #df = df[df["Earnings Announcements (Upcoming)"] > 5];

    df = df.drop_duplicates(subset='Symbol')

    return df



def chartAnalysis():

    # Use glob to find all files that start with "screener_results"
    file_list = glob.glob("screener_results*.xls")

    if not file_list:
        print("No files found with the pattern 'screener_results*.xls'")
        return

    df = pd.DataFrame()

    # Concatenate all files matching the pattern
    for file in file_list:
        df_temp = pd.read_excel(file)
        df = pd.concat([df, df_temp], ignore_index=True)

    # Drop duplicate rows based on the 'Symbol' column
    df = df.drop_duplicates(subset='Symbol')

    # add $volume to df, defined as Volume (10 Day Avg) in millions * df["Security Price"]

    df['$Volume'] = df["Volume (10 Day Avg)"]  * df["Security Price"]

    # Filter out rows where $Volume is less than 25
    df = df[df['$Volume'] >= 25]

    #print(df[['Symbol', '$Volume']].head(25))


    tickers = df["Symbol"].to_list()

    printFilteredData(tickers)


    filter = input("Earnings Filter needed(Y/N):")

    # Filter the screener DataFrame with CSV data
    screener = groupAndFilter(df, filter)

    printFilteredData( tickers = screener["Symbol"].to_list())

    # done = ["AAPL","APP","GOOGL"]
    # screener = screener[screener['Symbol'].isin(done)]

    df_sorted = screener

    tickers = df_sorted["Symbol"].to_list()

    printFilteredData(tickers)


    print("Start? (Y/N):")

    start = input()


    if start.strip().upper() == 'Y':
       df_sorted.to_excel("sorted_screener.xlsx")
       save_file = input('Save File Y/N: ').strip().lower() == 'y'
       print(save_file)
       if save_file:
        downloadChartsFidelityV2(df_sorted, "fidelity_screen")
       else :
         downloadChartsFidelityV2(df_sorted)

chartAnalysis()
