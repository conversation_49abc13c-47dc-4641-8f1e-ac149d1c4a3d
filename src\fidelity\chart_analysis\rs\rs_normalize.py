#!/usr/bin/env python3
"""
winsorize_rs.py

Winsorize Relative Strength, recompute 1..99 percentiles, re-rank, and
emit an RSRATING-like thresholds file based on the winsorized distribution.

Usage:
  python winsorize_rs.py \
      --in rs_stocks.csv \
      --out rs_stocks_winsorized.csv \
      --rating-out RSRATING_winsor.csv \
      --low 0.01 --high 0.99
"""

import argparse
import os
from datetime import datetime
import numpy as np
import pandas as pd


def percentile_rank_1_99(values: np.ndarray, x: float) -> int:
    """Match your build script's 1..99 percentile rank logic."""
    if len(values) < 2 or not np.isfinite(x):
        return 1
    pct = np.sum(values < x) / (len(values) - 1)
    return int(np.clip(1 + 98 * pct, 1, 99))


def main():
    ap = argparse.ArgumentParser(description="Winsorize RS and rebuild ranks & thresholds.")
    ap.add_argument("--in", dest="in_path", default="output/rs_stocks.csv")
    ap.add_argument("--out", dest="out_path", default=r"src\data\rs_data\rs_stocks_winsorized.csv",
                    help="Path to save winsorized table CSV")
    ap.add_argument("--rating-out", dest="rating_out", default=r"src\data\rs_data\RSRATING_winsor.csv",
                    help="Path to save winsorized thresholds CSV")
    ap.add_argument("--low", dest="low", type=float, default=0.01,
                    help="Lower quantile cap (default 0.01 = 1%)")
    ap.add_argument("--high", dest="high", type=float, default=0.99,
                    help="Upper quantile cap (default 0.99 = 99%)")
    args = ap.parse_args()

    in_path = args.in_path
    out_path = args.out_path
    rating_out = args.rating_out
    q_low = float(args.low)
    q_high = float(args.high)

    if not (0.0 <= q_low < q_high <= 1.0):
        raise ValueError("--low and --high must satisfy 0 <= low < high <= 1")

    # --- Load ---
    if not os.path.exists(in_path):
        raise FileNotFoundError(f"Input file not found: {in_path}")
    df = pd.read_csv(in_path)

    required_cols = {"Ticker", "Relative Strength"}
    missing = required_cols - set(df.columns)
    if missing:
        raise ValueError(f"Input missing required columns: {missing}")

    # --- Winsorize Relative Strength ---
    rs = df["Relative Strength"].astype(float)
    lo = rs.quantile(q_low)
    hi = rs.quantile(q_high)
    df["Relative Strength"] = rs.clip(lo, hi)

    # --- Recompute Percentiles (1..99) ---
    values = df["Relative Strength"].to_numpy(dtype=float)
    df["Percentile"] = [percentile_rank_1_99(values, x) for x in values]

    # --- Re-rank ---
    df = df.sort_values(["Percentile", "Relative Strength"], ascending=[False, False]).reset_index(drop=True)
    df["Rank"] = np.arange(1, len(df) + 1)

    # Keep a friendly column order if present
    ordered_cols = ["Rank", "Ticker", "Exchange", "Relative Strength", "Percentile"]
    cols = [c for c in ordered_cols if c in df.columns] + [c for c in df.columns if c not in ordered_cols]
    df = df[cols]

    # --- Save winsorized table ---
    df.to_csv(out_path, index=False)

    # --- Compute & write thresholds (winsorized) ---
    # Using the winsorized values in 'values'
    percentiles = [98, 90, 70, 50, 30, 10, 2]
    thresholds = np.percentile(values, percentiles).tolist()
    thresholds = [round(float(x), 2) for x in thresholds]
    ts = datetime.utcnow().strftime("%Y%m%dT")

    header = ["timestamp", "P98", "P90", "P70", "P50", "P30", "P10", "P02", "CapLow", "CapHigh"]
    row = [ts] + thresholds + [round(float(lo), 2), round(float(hi), 2)]

    if not os.path.exists(rating_out):
        pd.DataFrame([row], columns=header).to_csv(rating_out, index=False)
    else:
        # append without header
        pd.DataFrame([row]).to_csv(rating_out, mode="a", index=False, header=False)

    # --- Console summary ---
    print(f"[winsorize_rs] Input:  {in_path}")
    print(f"[winsorize_rs] Output: {out_path}")
    print(f"[winsorize_rs] RSRATING (winsorized): {rating_out}")
    print(f"[winsorize_rs] Caps: low={q_low:.4f} → {lo:.4f}, high={q_high:.4f} → {hi:.4f}")
    print(f"[winsorize_rs] Thresholds (winsorized):")
    for p, t in zip(percentiles, thresholds):
        print(f"  P{p:02d}: {t}")


if __name__ == "__main__":
    main()
