#!/usr/bin/env python3
"""
Test script to verify that the Series comparison fixes work correctly using real AAOI data.
"""

import pandas as pd
import sys
import os
import asyncio
import aiohttp

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from fidelity.chart_analysis.analysis import calculate_signal_score, get_signal_strength, determine_exclusion_reasons
from stockdata.data_source import getStockDataV3

async def get_real_aaoi_data():
    """Fetch real AAOI data to test with actual problematic data"""
    print("Fetching real AAOI data...")

    async with aiohttp.ClientSession() as session:
        try:
            df = await getStockDataV3(session, 'AAOI')
            return df
        except Exception as e:
            print(f"Error fetching AAOI data: {e}")
            return None

def test_real_aaoi():
    """Test with real AAOI data that was causing the Series comparison error"""

    print("=== TESTING REAL AAOI DATA ===\n")

    # Get real AAOI data
    df = asyncio.run(get_real_aaoi_data())

    if df is None:
        print("❌ Could not fetch AAOI data")
        return

    print(f"✅ Successfully fetched AAOI data")
    print(f"DataFrame shape: {df.shape}")
    print(f"Columns: {list(df.columns)}")
    print()

    # Show some sample data to understand the structure
    print("Sample data (first few columns):")
    if not df.empty:
        print(df.head())
        print()

        # Check for problematic data types
        print("Data types:")
        for col in df.columns:
            if col in ['Close', '50', '150', '200', 'slope_50', 'slope_200', 'UDRatio', '$VolumeM', 'RS_Rating']:
                print(f"  {col}: {df[col].dtype} - Sample value: {df[col].iloc[0] if not df[col].empty else 'N/A'}")
        print()

    # Create a mock row like the screener would have
    mock_row = {
        'Symbol': 'AAOI',
        'MOMO': False,  # Assuming not a MOMO stock
        '$VolumeM': df['$VolumeM'].iloc[0] if '$VolumeM' in df.columns and not df.empty else 0.0
    }

    print("Testing signal score calculation with real AAOI data:")
    print("-" * 50)

    try:
        # Enable detailed error tracking
        import traceback
        import warnings

        # Capture warnings to see exactly where the Series error comes from
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")

            # This is where the error was occurring
            score = calculate_signal_score(mock_row, df)
            strength = get_signal_strength(score)

            print(f"✅ Signal Score: {score:.1f}")
            print(f"✅ Signal Strength: {strength}")

            # Check if we caught any warnings
            if w:
                print(f"⚠️ Warnings caught: {len(w)}")
                for warning in w:
                    print(f"  Warning: {warning.message}")
                    print(f"  File: {warning.filename}:{warning.lineno}")
                    print(f"  Category: {warning.category.__name__}")
            else:
                print("✅ No warnings detected!")

    except Exception as e:
        print(f"❌ Error in signal calculation: {e}")
        print(f"Error type: {type(e).__name__}")

        # Print full traceback to see exactly where the error occurs
        print("\nFull traceback:")
        traceback.print_exc()

        # Let's debug what's causing the issue
        print("\nDebugging the error:")
        print("Checking individual data access...")

        try:
            # Check multi-level column access
            print("Available columns:")
            for i, col in enumerate(df.columns[:10]):  # Show first 10 columns
                print(f"  {i}: {col} (type: {type(col)})")

            # Try accessing Close with multi-level approach
            close_cols = [col for col in df.columns if isinstance(col, tuple) and col[0] == 'Close']
            if close_cols:
                close_col = close_cols[0]
                close_val = df[close_col].iloc[0]
                print(f"  Close value from {close_col}: {close_val} (type: {type(close_val)})")

        except Exception as debug_error:
            print(f"  ❌ Debug error: {debug_error}")

    print()

    # Test exclusion reasons
    print("Testing exclusion reasons:")
    try:
        reasons = determine_exclusion_reasons(df)
        print(f"✅ Exclusion reasons: {reasons}")
    except Exception as e:
        print(f"❌ Error in exclusion reasons: {e}")

    print()

def create_synthetic_problematic_data():
    """Create synthetic data that mimics the problematic AAOI data structure"""

    # Create data that might cause Series comparison issues
    problematic_data = {
        'Close': [45.23],
        '50': [42.15],
        '100': [41.80],
        '150': [40.95],
        '200': [39.50],
        'TTM': [False],  # Boolean value
        'slope_50': [-0.1],  # Negative slope
        'slope_200': [-0.05],
        'UDRatio': [0.65],  # Below threshold
        '$VolumeM': [15.5],  # Below 25M threshold
        'RS_Rating': [75.0],  # Below 80 threshold
        'EPS_TTM': [-1.25],  # Negative EPS
        'EPS_Last_Q': [-0.35],
        'Rev_TTM': [125.0],
        'Rev_Last_Q': [28.5],
        'Proj_EPS': [0.15],
        'Performance_1Y': [-15.2]  # Negative performance
    }

    return pd.DataFrame(problematic_data)

def test_synthetic_problematic_data():
    """Test with synthetic data that mimics AAOI's problematic characteristics"""

    print("=== TESTING SYNTHETIC PROBLEMATIC DATA ===\n")

    df = create_synthetic_problematic_data()

    print("Testing with synthetic data that mimics AAOI issues:")
    print(f"DataFrame shape: {df.shape}")
    print(f"Sample values:")
    for col in ['Close', '50', '150', '200', 'slope_50', 'UDRatio', '$VolumeM', 'RS_Rating']:
        if col in df.columns:
            val = df[col].iloc[0]
            print(f"  {col}: {val} (type: {type(val)})")
    print()

    # Create mock row
    mock_row = {
        'Symbol': 'SYNTHETIC',
        'MOMO': False,
        '$VolumeM': df['$VolumeM'].iloc[0]
    }

    try:
        score = calculate_signal_score(mock_row, df)
        strength = get_signal_strength(score)
        reasons = determine_exclusion_reasons(df)

        print(f"✅ Signal Score: {score:.1f}")
        print(f"✅ Signal Strength: {strength}")
        print(f"✅ Exclusion Reasons: {reasons}")
        print("✅ No Series comparison errors!")

    except Exception as e:
        print(f"❌ Error: {e}")
        print(f"Error type: {type(e).__name__}")

    print()

def test_edge_cases():
    """Test edge cases that previously caused Series comparison errors"""

    print("=== TESTING EDGE CASES ===\n")

    # Test case: DataFrame with Series that would cause ambiguous truth value
    edge_data = {
        'Close': [100.0, 101.0],  # Multiple values to create Series
        '50': [95.0, 96.0],
        '150': [85.0, 86.0],
        '200': [80.0, 81.0],
        'slope_50': [0.5, 0.6],
        'slope_200': [0.3, 0.4],
        'UDRatio': [0.8, 0.9],
        '$VolumeM': [50.0, 55.0],
        'RS_Rating': [85.0, 87.0]
    }

    df = pd.DataFrame(edge_data)
    mock_row = {'Symbol': 'EDGE', 'MOMO': False, '$VolumeM': 50.0}

    print("Testing DataFrame with multiple rows (potential Series issues):")
    try:
        score = calculate_signal_score(mock_row, df)
        strength = get_signal_strength(score)
        reasons = determine_exclusion_reasons(df)

        print(f"  Signal Score: {score:.1f}")
        print(f"  Signal Strength: {strength}")
        print(f"  Exclusion Reasons: {reasons}")
        print("  ✅ Successfully handled multi-row DataFrame!")

    except Exception as e:
        print(f"  ❌ Error with multi-row DataFrame: {e}")

if __name__ == "__main__":
    # Test with real AAOI data first
    test_real_aaoi()

    # Test with synthetic problematic data
    test_synthetic_problematic_data()

    # Test edge cases
    test_edge_cases()

    print("\n🎯 REAL AAOI SERIES COMPARISON TEST COMPLETED!")
    print("\nThis test specifically targets the AAOI error:")
    print("• 'The truth value of a Series is ambiguous' errors")
    print("• Real data structure and values that caused the original issue")
    print("• Proper handling of negative values, low thresholds, and edge cases")
    print("• Defensive coding with null checking and type safety")
    print("\nIf this test passes, the AAOI error should be resolved! 🚀")
