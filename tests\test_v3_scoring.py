#!/usr/bin/env python3
"""
Test script for the new v3 momentum + earnings acceleration scoring system
"""

import sys
import os
import pandas as pd
import json

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_v3_scoring():
    """Test the v3 scoring system with sample data"""
    
    print("🚀 TESTING V3 MOMENTUM + EARNINGS ACCELERATION SCORING")
    print("=" * 60)
    
    try:
        from fidelity.chart_analysis.analysis import (
            calculate_signal_score_v3, 
            calculate_signal_score_v3_with_diagnostics,
            get_signal_strength
        )
        
        print("✅ Successfully imported v3 scoring functions")
        
        # Create comprehensive test cases
        test_cases = [
            {
                'name': 'High-Growth Momentum Stock',
                'description': 'Strong earnings acceleration + high RS + good trend',
                'row': pd.Series({
                    'Symbol': 'GROWTH',
                    'RS_Rating': 95,  # 20 pts (full)
                    'Price Performance (13 Weeks)': 60,  # 10 pts (full at 50%+)
                    'Price Performance (52 Weeks)': 150,  # 5 pts (full at 100%+)
                    'EPS Growth (Last Qtr vs. Same Qtr Prior Yr)': 80,  # 13 pts (80% of max)
                    'EPS Growth (TTM vs Prior TTM)': 120,  # 12 pts (full)
                    'EPS Growth (Proj Next Yr vs. This Yr)': 40,  # 10 pts (80% of max)
                    '$Volume': 75  # Good volume
                }),
                'df': pd.DataFrame({
                    'Close': [100],
                    '50': [85],    # 17.6% above
                    '100': [80],   # 25% above  
                    '150': [75],   # 33% above
                    '200': [70],   # 42% above (all full MA scores)
                    'slope_50': [0.8],    # Positive
                    'slope_200': [0.5],   # Positive
                    'UDRatio': [1.4],     # Strong accumulation
                    '$VolumeM': [80],     # High volume
                    'TTM': [True]         # Active squeeze
                }),
                'expected_range': [85, 100]
            },
            {
                'name': 'Value Turnaround Play',
                'description': 'Moderate earnings growth + decent RS + weak trend',
                'row': pd.Series({
                    'Symbol': 'VALUE',
                    'RS_Rating': 75,  # 5 pts (75-70)/30 * 20
                    'Price Performance (13 Weeks)': 25,  # 5 pts (25/50 * 10)
                    'Price Performance (52 Weeks)': 40,  # 2 pts (40/100 * 5)
                    'EPS Growth (Last Qtr vs. Same Qtr Prior Yr)': 30,  # ~4 pts
                    'EPS Growth (TTM vs Prior TTM)': 25,  # ~3 pts
                    'EPS Growth (Proj Next Yr vs. This Yr)': 15,  # ~3 pts
                    '$Volume': 35  # Moderate volume
                }),
                'df': pd.DataFrame({
                    'Close': [50],
                    '50': [52],    # Slightly below 50MA
                    '100': [48],   # Above 100MA
                    '150': [45],   # Above 150/200
                    '200': [42],
                    'slope_50': [-0.1],   # Negative short-term
                    'slope_200': [0.2],   # Positive long-term
                    'UDRatio': [1.1],     # Slight accumulation
                    '$VolumeM': [35],     # Moderate volume
                    'TTM': [False]        # No squeeze
                }),
                'expected_range': [20, 40]
            },
            {
                'name': 'Weak Declining Stock',
                'description': 'Poor fundamentals + low RS + bad trend',
                'row': pd.Series({
                    'Symbol': 'WEAK',
                    'RS_Rating': 45,  # 0 pts (below 70 floor)
                    'Price Performance (13 Weeks)': -10,  # 0 pts
                    'Price Performance (52 Weeks)': -20,  # 0 pts
                    'EPS Growth (Last Qtr vs. Same Qtr Prior Yr)': -15,  # 0 pts
                    'EPS Growth (TTM vs Prior TTM)': -5,  # 0 pts
                    'EPS Growth (Proj Next Yr vs. This Yr)': 5,  # ~1 pt
                    '$Volume': 15  # Low volume
                }),
                'df': pd.DataFrame({
                    'Close': [30],
                    '50': [35],    # Below all MAs
                    '100': [38],
                    '150': [40],
                    '200': [42],
                    'slope_50': [-0.5],   # Negative slopes
                    'slope_200': [-0.3],
                    'UDRatio': [0.8],     # Distribution
                    '$VolumeM': [15],     # Low volume
                    'TTM': [False]        # No squeeze
                }),
                'expected_range': [0, 15]
            }
        ]
        
        print(f"\n📊 Testing {len(test_cases)} scenarios:")
        print("-" * 60)
        
        all_passed = True
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n{i}. {test_case['name']}")
            print(f"   Description: {test_case['description']}")
            print(f"   Symbol: {test_case['row']['Symbol']}")
            
            # Calculate score with diagnostics
            score, diagnostics = calculate_signal_score_v3_with_diagnostics(
                test_case['row'], test_case['df']
            )
            signal_strength = get_signal_strength(score)
            
            expected_min, expected_max = test_case['expected_range']
            
            print(f"   Expected Range: {expected_min}-{expected_max}")
            print(f"   Actual Score:   {score:.1f}")
            print(f"   Signal:         {signal_strength}")
            
            # Check if score is in expected range
            if expected_min <= score <= expected_max:
                print(f"   ✅ PASS - Score within expected range")
            else:
                print(f"   ❌ FAIL - Score outside expected range")
                all_passed = False
            
            # Show pillar breakdown
            pillars = diagnostics["pillars"]
            print(f"   📊 Pillar Breakdown:")
            print(f"      Earnings:   {pillars['earnings']:5.1f}/35")
            print(f"      Momentum:   {pillars['momentum']:5.1f}/35")
            print(f"      Trend:      {pillars['trend']:5.1f}/20")
            print(f"      Liquidity:  {pillars['liquidity']:5.1f}/10")
            
            # Show key inputs
            inputs = diagnostics["inputs"]
            print(f"   🔍 Key Inputs:")
            print(f"      RS Rating:  {inputs.get('RS', 'N/A')}")
            print(f"      EPS TTM:    {inputs.get('EPS_TTM', 'N/A')}%")
            print(f"      P13W:       {inputs.get('P13', 'N/A')}%")
            print(f"      Volume:     ${inputs.get('VolM', 'N/A')}M")
            print(f"      TTM:        {inputs.get('TTM', 'N/A')}")
        
        print(f"\n" + "=" * 60)
        if all_passed:
            print("🎉 ALL TESTS PASSED!")
            print("\nV3 Scoring System Features:")
            print("✅ Earnings Acceleration (35 pts) - Rewards growth acceleration")
            print("✅ Momentum & RS (35 pts) - High RS + recent performance")
            print("✅ Trend Quality (20 pts) - MA position + slopes + TTM")
            print("✅ Liquidity (10 pts) - Volume + accumulation")
            print("✅ Continuous Scaling - No harsh cliffs")
            print("✅ Detailed Diagnostics - See exactly what drives the score")
        else:
            print("❌ SOME TESTS FAILED")
            print("Please check the scoring logic")
        
        # Show sample diagnostics JSON
        if test_cases:
            print(f"\n📋 Sample Diagnostics JSON:")
            print("-" * 30)
            sample_score, sample_diag = calculate_signal_score_v3_with_diagnostics(
                test_cases[0]['row'], test_cases[0]['df']
            )
            print(json.dumps(sample_diag, indent=2, default=str))
            
        return all_passed
        
    except ImportError as e:
        print(f"❌ Could not import v3 scoring functions: {e}")
        return False
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 V3 MOMENTUM + EARNINGS ACCELERATION SCORING TEST")
    print("=" * 70)
    
    success = test_v3_scoring()
    
    if success:
        print(f"\n✅ V3 scoring system is ready!")
        print(f"\nTo use in production:")
        print(f"1. Replace calculate_signal_score_equal_weight with calculate_signal_score_v3")
        print(f"2. Optionally use calculate_signal_score_v3_with_diagnostics for analysis")
        print(f"3. Tune SCORER_CFG weights/scales based on backtesting")
        print(f"4. Monitor pillar contributions for portfolio balance")
    else:
        print(f"\n❌ Tests failed - please check the implementation")
