#!/usr/bin/env python3
"""
Test script for the new equal-weight signal scoring system
"""

import sys
import os
import pandas as pd

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_equal_weight_scoring():
    """Test the equal-weight scoring system with sample data"""
    
    print("🧪 TESTING EQUAL-WEIGHT SIGNAL SCORING")
    print("=" * 50)
    
    try:
        from fidelity.chart_analysis.analysis import calculate_signal_score_equal_weight, get_signal_strength
        
        print("✅ Successfully imported equal-weight scoring function")
        
        # Create sample test data
        test_cases = [
            {
                'name': 'Perfect Stock (All 10 Criteria)',
                'row': pd.Series({
                    'Symbol': 'PERFECT',
                    'RS_Rating': 95,
                    'Price Performance (52 Weeks)': 150,
                    'Revenue Growth (TTM vs. Prior TTM)': 30,
                    '$Volume': 75,
                    'EPS Growth (TTM vs Prior TTM)': 50,
                    'TTM': True,
                    'Price Performance (13 Weeks)': 60,
                    'Equity Summary Score (ESS) from LSEG StarMine': 9
                }),
                'df': pd.DataFrame({
                    'Close': [100],
                    '50': [90],
                    '100': [85],
                    '150': [80],
                    '200': [75],
                    'slope_50': [0.5],
                    'slope_200': [0.3],
                    '$VolumeM': [60]
                }),
                'expected_score': 100
            },
            {
                'name': 'Strong Stock (4 Criteria - Your Example)',
                'row': pd.Series({
                    'Symbol': 'STRONG',
                    'RS_Rating': 92,  # 🚀 Exceptional RS Rating
                    'Price Performance (52 Weeks)': 120,  # 📈 100%+ Annual Gain
                    'Revenue Growth (TTM vs. Prior TTM)': 35,  # 🏢 Exceptional Revenue Growth
                    '$Volume': 1200,  # 🌊 High Volume ($1B+)
                    'EPS Growth (TTM vs Prior TTM)': 15,  # Below 25% threshold
                    'TTM': False,
                    'Price Performance (13 Weeks)': 25,  # Below 50% threshold
                    'Equity Summary Score (ESS) from LSEG StarMine': 6  # Below 8 threshold
                }),
                'df': pd.DataFrame({
                    'Close': [50],
                    '50': [55],  # Price below some MAs
                    '100': [45],
                    '150': [40],
                    '200': [35],
                    'slope_50': [-0.1],  # Negative slope
                    'slope_200': [0.2],
                    '$VolumeM': [30]  # Below 50M threshold
                }),
                'expected_score': 40  # 4 criteria * 10 points each
            },
            {
                'name': 'Weak Stock (1 Criterion)',
                'row': pd.Series({
                    'Symbol': 'WEAK',
                    'RS_Rating': 75,  # Below 90 threshold
                    'Price Performance (52 Weeks)': 25,  # Below 100% threshold
                    'Revenue Growth (TTM vs. Prior TTM)': 15,  # Below 25% threshold
                    '$Volume': 20,  # Below 50M threshold
                    'EPS Growth (TTM vs Prior TTM)': 35,  # 💪 Strong EPS Growth (only one that qualifies)
                    'TTM': False,
                    'Price Performance (13 Weeks)': 15,  # Below 50% threshold
                    'Equity Summary Score (ESS) from LSEG StarMine': 5  # Below 8 threshold
                }),
                'df': pd.DataFrame({
                    'Close': [30],
                    '50': [35],  # Price below MAs
                    '100': [32],
                    '150': [28],
                    '200': [25],
                    'slope_50': [-0.2],  # Negative slopes
                    'slope_200': [-0.1],
                    '$VolumeM': [15]  # Below 50M threshold
                }),
                'expected_score': 10  # 1 criterion * 10 points
            }
        ]
        
        print(f"\n📊 Testing {len(test_cases)} scenarios:")
        print("-" * 50)
        
        all_passed = True
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n{i}. {test_case['name']}")
            print(f"   Symbol: {test_case['row']['Symbol']}")
            
            # Calculate score
            actual_score = calculate_signal_score_equal_weight(test_case['row'], test_case['df'])
            signal_strength = get_signal_strength(actual_score)
            expected_score = test_case['expected_score']
            
            print(f"   Expected Score: {expected_score}")
            print(f"   Actual Score:   {actual_score}")
            print(f"   Signal:         {signal_strength}")
            
            # Check if score matches expectation (allow some tolerance for edge cases)
            if abs(actual_score - expected_score) <= 10:  # 10-point tolerance
                print(f"   ✅ PASS")
            else:
                print(f"   ❌ FAIL - Score mismatch")
                all_passed = False
            
            # Show which criteria were met for debugging
            row = test_case['row']
            df = test_case['df']
            criteria_met = []
            
            if row.get('RS_Rating', 0) >= 90:
                criteria_met.append("🚀 RS Rating 90+")
            if row.get('Price Performance (52 Weeks)', 0) >= 100:
                criteria_met.append("📈 100%+ Annual")
            if row.get('Revenue Growth (TTM vs. Prior TTM)', 0) >= 25:
                criteria_met.append("🏢 Revenue 25%+")
            if row.get('$Volume', 0) >= 50:
                criteria_met.append("🌊 Volume $50M+")
            if row.get('EPS Growth (TTM vs Prior TTM)', 0) >= 25:
                criteria_met.append("💪 EPS 25%+")
            if row.get('TTM', False):
                criteria_met.append("🎯 TTM Squeeze")
            if row.get('Price Performance (13 Weeks)', 0) >= 50:
                criteria_met.append("🔥 Recent 50%+")
            if row.get('Equity Summary Score (ESS) from LSEG StarMine', 0) >= 8:
                criteria_met.append("⭐ ESS 8+")
            
            # Check technical criteria
            if not df.empty:
                current_price = df['Close'].iloc[0]
                if (current_price > df['50'].iloc[0] and 
                    current_price > df['100'].iloc[0] and 
                    current_price > df['150'].iloc[0] and 
                    current_price > df['200'].iloc[0]):
                    criteria_met.append("📊 Above All MAs")
                
                if df['slope_50'].iloc[0] > 0 and df['slope_200'].iloc[0] > 0:
                    criteria_met.append("📈 Positive Slopes")
            
            print(f"   Criteria Met: {len(criteria_met)}/10")
            for criterion in criteria_met:
                print(f"     • {criterion}")
        
        print(f"\n" + "=" * 50)
        if all_passed:
            print("🎉 ALL TESTS PASSED!")
            print("\nEqual-weight scoring system is working correctly:")
            print("• Each criterion = 10 points")
            print("• Maximum score = 100 points")
            print("• Clear, transparent scoring")
            print("• Easy to understand which criteria are met")
        else:
            print("❌ SOME TESTS FAILED")
            print("Please check the scoring logic")
            
        return all_passed
        
    except ImportError as e:
        print(f"❌ Could not import scoring functions: {e}")
        return False
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        return False

if __name__ == "__main__":
    print("🚀 EQUAL-WEIGHT SIGNAL SCORING TEST")
    print("=" * 60)
    
    success = test_equal_weight_scoring()
    
    if success:
        print(f"\n✅ Equal-weight scoring system is ready!")
        print(f"\nNow when you run your analysis:")
        print(f"• Stocks will be scored using 10 equal criteria")
        print(f"• Each criterion contributes exactly 10 points")
        print(f"• UI will show which criteria are met")
        print(f"• Scoring is transparent and easy to understand")
    else:
        print(f"\n❌ Tests failed - please check the implementation")
