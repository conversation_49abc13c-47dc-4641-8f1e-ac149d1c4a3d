#!/usr/bin/env python3
"""
Test the new signal-based UI display
"""

import sys
sys.path.append('src')

from fidelity.chart_analysis.web_ui import _get_signal_highlights

def test_signal_ui_display():
    """Test how the signal highlights look in the UI"""

    print("=== TESTING SIGNAL UI DISPLAY ===\n")

    # Test cases with different signal strengths
    test_cases = [
        {
            'name': 'Strong Buy Stock',
            'data': {
                'Symbol': 'IREN',
                'Signal_Score': 97.0,
                'Signal': 'STRONG BUY',
                'RS_Rating': 98,
                'Price Performance (52 Weeks)': 370.47,
                'EPS Growth (TTM vs Prior TTM)': 165.43,
                'Revenue Growth (TTM vs. Prior TTM)': 214.49,
                '$Volume': 1296.64,
                'TTM': True
            }
        },
        {
            'name': 'Buy Stock',
            'data': {
                'Symbol': 'CIFR',
                'Signal_Score': 77.0,
                'Signal': 'BUY',
                'RS_Rating': 98,
                'Price Performance (52 Weeks)': 308.62,
                'EPS Growth (TTM vs Prior TTM)': -24.86,
                'Revenue Growth (TTM vs. Prior TTM)': 0.11,
                '$Volume': 504.12,
                'TTM': True
            }
        },
        {
            'name': 'Moderate Buy Stock',
            'data': {
                'Symbol': 'AAPL',
                'Signal_Score': 58.7,
                'Signal': 'MODERATE BUY',
                'RS_Rating': 85,
                'Price Performance (52 Weeks)': 25.8,
                'EPS Growth (TTM vs Prior TTM)': 15.2,
                'Revenue Growth (TTM vs. Prior TTM)': 8.2,
                '$Volume': 12400.0,
                'TTM': False
            }
        },
        {
            'name': 'No Signal Data',
            'data': {
                'Symbol': 'TEST',
                'RS_Rating': 75,
                'Price Performance (52 Weeks)': 15.2,
                'EPS Growth (TTM vs Prior TTM)': 8.5,
                'Revenue Growth (TTM vs. Prior TTM)': 5.2,
                '$Volume': 150.0
            }
        }
    ]

    for test_case in test_cases:
        print(f"=== {test_case['name']} ({test_case['data']['Symbol']}) ===")

        # Generate the signal highlights HTML
        highlights_html = _get_signal_highlights(test_case['data'])

        # Create a complete table for display
        full_html = f"""
        <table class="info">
            <tr><th>Classification</th><td style="font-size: 0.9em;">Technology | Software | Application Software</td></tr>
            {highlights_html}
        </table>
        """

        # Convert HTML to readable format for console
        readable = full_html.replace('<table class="info">', '').replace('</table>', '')
        readable = readable.replace('<tr><th>', '  ').replace('</th><td style="font-size: 0.9em;">', ': ').replace('</th><td>', ': ').replace('</td></tr>', '')
        readable = readable.replace('<tr><td colspan=\'2\' style=\'padding: 3px 8px; font-size: 0.9em; color: #374151; border-left: 3px solid #e5e7eb; background-color: #f9fafb;\'>', '    • ')
        readable = readable.replace('</td></tr>', '')
        readable = readable.replace('<strong style="color: #22c55e;">', '').replace('<strong style="color: #3b82f6;">', '')
        readable = readable.replace('<strong style="color: #f59e0b;">', '').replace('<strong style="color: #6b7280;">', '')
        readable = readable.replace('<strong style="color: #ef4444;">', '').replace('<strong style="color: #dc2626;">', '')
        readable = readable.replace('</strong>', '')
        readable = readable.replace('<span style="color: #666;">', '').replace('</span>', '')

        print(readable)
        print("-" * 50)

    print("\n✅ Signal UI display test completed!")
    print("\nThe new UI will show:")
    print("• 🎯 Signal filter dropdown (first filter, highlighted in blue)")
    print("• 📊 Single-line classification: Sector | Industry | Sub-Industry")
    print("• 🚀 Signal strength with color coding")
    print("• 📈 Numerical score out of 100")
    print("• ⭐ Key highlights (RS rating, performance, growth, volume)")
    print("• 🎯 TTM squeeze status")
    print("• ✨ Clean, focused information instead of raw metrics")
    print("• 🔍 Filter by signal strength (STRONG BUY, BUY, etc.)")

if __name__ == "__main__":
    test_signal_ui_display()
