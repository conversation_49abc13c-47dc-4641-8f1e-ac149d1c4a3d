"""
Utility functions for fetching and adding company descriptions to stock data.
"""

import pandas as pd
import asyncio
import yfinance as yf
from functools import partial

# Import session for yfinance
try:
    from common.http_session import get_session
    session = get_session('src/certs/my_ca_bundle.pem')
except ImportError:
    print("Warning: Could not import session. Using default session.")
    session = None

async def _run_sync_in_executor(func, *args, **kwargs):
    """Run synchronous function in executor to make it async."""
    loop = asyncio.get_running_loop()
    return await loop.run_in_executor(None, partial(func, *args, **kwargs))

async def fetch_description(symbol: str) -> str:
    """
    Fetch company description using yfinance.
    
    Returns:
    - Company description string
    """
    try:
        ticker_data = await _run_sync_in_executor(yf.Ticker, symbol, session=session)
        info = await _run_sync_in_executor(lambda: ticker_data.info)
        
        description = info.get('longBusinessSummary', 'N/A')
        return description if description and description != 'N/A' else 'No description available'
        
    except Exception as e:
        print(f"Error fetching description for {symbol}: {e}")
        return f'Error fetching description'

def add_descriptions_sync(df: pd.DataFrame) -> pd.DataFrame:
    """
    Synchronous wrapper to add descriptions to dataframe.
    This can be called from non-async contexts.
    """
    if 'Symbol' not in df.columns:
        print("Warning: No 'Symbol' column found in dataframe")
        return df
    
    print(f"📝 Fetching descriptions for {len(df)} stocks...")
    
    # Check if Description column already exists
    if 'Description' not in df.columns:
        df['Description'] = ''
    
    # Get unique symbols to avoid duplicate API calls
    symbols = df['Symbol'].unique()
    
    for i, symbol in enumerate(symbols, 1):
        if pd.isna(symbol) or symbol == '':
            continue
            
        print(f"[{i}/{len(symbols)}] Fetching description for {symbol}...")
        
        try:
            # Use synchronous yfinance call
            ticker = yf.Ticker(str(symbol).strip().upper(), session=session)
            info = ticker.info
            description = info.get('longBusinessSummary', 'No description available')
            
            if not description or description == 'N/A':
                description = 'No description available'
                
            # Update all rows with this symbol
            df.loc[df['Symbol'] == symbol, 'Description'] = description
            
        except Exception as e:
            print(f"Error fetching description for {symbol}: {e}")
            df.loc[df['Symbol'] == symbol, 'Description'] = 'Error fetching description'
    
    print("✅ Descriptions added successfully!")
    return df

async def add_descriptions_async(df: pd.DataFrame) -> pd.DataFrame:
    """
    Async version to add descriptions to dataframe.
    Use this when calling from async contexts.
    """
    if 'Symbol' not in df.columns:
        print("Warning: No 'Symbol' column found in dataframe")
        return df
    
    print(f"📝 Fetching descriptions for {len(df)} stocks...")
    
    # Check if Description column already exists
    if 'Description' not in df.columns:
        df['Description'] = ''
    
    # Get unique symbols to avoid duplicate API calls
    symbols = df['Symbol'].unique()
    descriptions = {}
    
    for i, symbol in enumerate(symbols, 1):
        if pd.isna(symbol) or symbol == '':
            continue
            
        print(f"[{i}/{len(symbols)}] Fetching description for {symbol}...")
        description = await fetch_description(str(symbol).strip().upper())
        descriptions[symbol] = description
        
        # Small delay to be respectful to APIs
        await asyncio.sleep(0.1)
    
    # Map descriptions back to the dataframe
    df['Description'] = df['Symbol'].map(descriptions).fillna('No description available')
    
    print("✅ Descriptions added successfully!")
    return df
