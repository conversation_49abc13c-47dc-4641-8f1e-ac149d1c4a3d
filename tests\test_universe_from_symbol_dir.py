#!/usr/bin/env python3
"""
Simple unit test for load_stocks_only_from_symbol_dir function
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from fidelity.chart_analysis.rs.universe_from_symbol_dir import load_stocks_only_from_symbol_dir

def test_load_stocks_basic():
    """Test basic functionality of load_stocks_only_from_symbol_dir"""
    print("Testing load_stocks_only_from_symbol_dir...")
    
    try:
        # Test with default parameters
        df = load_stocks_only_from_symbol_dir()
        
        print(f"✓ Function executed successfully")
        print(f"✓ Returned DataFrame with {len(df)} rows")
        print(f"✓ Columns: {df.columns.tolist()}")
        
        # Check for required columns
        required_cols = ['Symbol', 'Exchange', 'SecurityName']
        for col in required_cols:
            assert col in df.columns, f"Missing required column: {col}"
        print(f"✓ All required columns present")
        
        # Check for ADR stocks specifically
        adr_stocks = ['BABA', 'PONY', 'JD', 'BIDU', 'NIO', 'XPEV', 'TSM', 'ASML']
        found_adrs = df[df['Symbol'].isin(adr_stocks)]
        
        print(f"\n=== ADR Stock Check ===")
        print(f"Looking for: {adr_stocks}")
        print(f"Found {len(found_adrs)} ADR stocks:")
        
        if len(found_adrs) > 0:
            print(found_adrs[['Symbol', 'Exchange', 'SecurityName']].to_string())
        else:
            print("❌ No ADR stocks found!")
            
            # Show some sample stocks for comparison
            print(f"\nSample of stocks that were found:")
            print(df.head(10)[['Symbol', 'Exchange', 'SecurityName']].to_string())
        
        # Test with ADR disabled
        print(f"\n=== Testing with allow_adr=False ===")
        df_no_adr = load_stocks_only_from_symbol_dir(allow_adr=False)
        found_adrs_no_adr = df_no_adr[df_no_adr['Symbol'].isin(adr_stocks)]
        print(f"With allow_adr=False, found {len(found_adrs_no_adr)} ADR stocks")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_raw_data_sources():
    """Test the raw data sources to see what's available"""
    print("\n=== Testing Raw Data Sources ===")
    
    try:
        from fidelity.chart_analysis.rs.universe_from_symbol_dir import _load_txt, NASDAQLISTED_URL, OTHERLISTED_URL
        
        # Test NASDAQ file
        print(f"Loading NASDAQ file: {NASDAQLISTED_URL}")
        nas_df = _load_txt(NASDAQLISTED_URL)
        print(f"✓ NASDAQ file loaded: {nas_df.shape} rows")
        print(f"  Columns: {nas_df.columns.tolist()}")
        
        # Test OTHER file  
        print(f"Loading OTHER file: {OTHERLISTED_URL}")
        oth_df = _load_txt(OTHERLISTED_URL)
        print(f"✓ OTHER file loaded: {oth_df.shape} rows")
        print(f"  Columns: {oth_df.columns.tolist()}")
        
        # Check for ADR stocks in raw data
        adr_stocks = ['BABA', 'PONY', 'JD', 'BIDU', 'NIO', 'XPEV']
        
        # Check NASDAQ file
        nas_adrs = nas_df[nas_df['Symbol'].isin(adr_stocks)]
        print(f"\nADR stocks in NASDAQ file: {len(nas_adrs)}")
        if len(nas_adrs) > 0:
            print(nas_adrs[['Symbol', 'Security Name']].to_string())
        
        # Check OTHER file - try different symbol columns
        symbol_cols = ['Symbol', 'ACT Symbol', 'CQS Symbol', 'NASDAQ Symbol']
        for col in symbol_cols:
            if col in oth_df.columns:
                oth_adrs = oth_df[oth_df[col].isin(adr_stocks)]
                print(f"\nADR stocks in OTHER file ({col}): {len(oth_adrs)}")
                if len(oth_adrs) > 0:
                    print(oth_adrs[[col, 'Security Name']].to_string())
                break
        
        return True
        
    except Exception as e:
        print(f"❌ Raw data test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("UNIT TEST: load_stocks_only_from_symbol_dir")
    print("=" * 60)
    
    # Run tests
    test1_passed = test_load_stocks_basic()
    test2_passed = test_raw_data_sources()
    
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"Basic functionality test: {'✓ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"Raw data sources test: {'✓ PASSED' if test2_passed else '❌ FAILED'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 All tests passed!")
    else:
        print("\n⚠️  Some tests failed - check output above for details")
