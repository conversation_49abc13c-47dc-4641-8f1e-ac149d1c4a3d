import os
import time
import threading
import mimetypes
import asyncio
import json
from http.server import Thread<PERSON><PERSON><PERSON>Server, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
from .data_manager import _read_likes, _get_snapshot_date_yyyy_mm_dd, LIKES_CSV_PATH, _ensure_data_dirs
from common.http_session import get_session

# Import dynamic chart ID manager
try:
    from .dynamic_chart_ids import initialize_chart_ids, get_current_chart_ids, refresh_chart_ids
    DYNAMIC_CHARTS_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Dynamic chart IDs not available: {e}")
    DYNAMIC_CHARTS_AVAILABLE = False

# In-memory dataset for the UI, will be populated by the main script
CURRENT_TICKERS = []
DAILY_CHART_ID = None
WEEKLY_CHART_ID = None
LIKE_SERVER_PORT = 5001

async def setup_dynamic_chart_ids():
    """Initialize dynamic chart IDs for daily and weekly charts"""
    global DAILY_CHART_ID, WEEKLY_CHART_ID

    if not DYNAMIC_CHARTS_AVAILABLE:
        print("⚠️ Dynamic chart IDs not available, using static fallbacks")
        return

    print("🎯 Setting up dynamic StockCharts IDs...")
    try:
        daily_id, weekly_id = await initialize_chart_ids()

        if daily_id:
            DAILY_CHART_ID = daily_id
            print(f"✅ Daily chart ID: {daily_id}")
        else:
            print("❌ Failed to generate daily chart ID")

        if weekly_id:
            WEEKLY_CHART_ID = weekly_id
            print(f"✅ Weekly chart ID (SMA 10,20): {weekly_id}")
        else:
            print("❌ Failed to generate weekly chart ID")

        if daily_id and weekly_id:
            print("🎉 Dynamic chart IDs successfully configured!")
        else:
            print("⚠️ Some chart IDs failed to generate, charts may not work properly")

    except Exception as e:
        print(f"❌ Error setting up dynamic chart IDs: {e}")

def refresh_chart_ids_sync():
    """Synchronous wrapper to refresh chart IDs"""
    if not DYNAMIC_CHARTS_AVAILABLE:
        return

    async def _refresh():
        global DAILY_CHART_ID, WEEKLY_CHART_ID
        try:
            daily_id, weekly_id = await refresh_chart_ids()
            if daily_id:
                DAILY_CHART_ID = daily_id
            if weekly_id:
                WEEKLY_CHART_ID = weekly_id
            print(f"🔄 Chart IDs refreshed - Daily: {daily_id}, Weekly: {weekly_id}")
        except Exception as e:
            print(f"❌ Error refreshing chart IDs: {e}")

    # Run in a new event loop if needed
    try:
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # If we're in an async context, schedule the task
            asyncio.create_task(_refresh())
        else:
            loop.run_until_complete(_refresh())
    except RuntimeError:
        # No event loop, create a new one
        asyncio.run(_refresh())

def _html_escape(s: str):
    return (s or '').replace('&', '&').replace('<', '<').replace('>', '>')

def _get_signal_highlights(row):
    """Generate signal strength highlights for display in web UI"""

    def safe_float(val, default=0.0):
        """Safely convert value to float"""
        if val is None or val == '':
            return default
        try:
            return float(val)
        except (ValueError, TypeError):
            return default

    def safe_get(row, key, default=''):
        """Safely get value from row"""
        return row.get(key, default) if row else default

    # Get signal data if available
    signal_score = safe_float(safe_get(row, 'Signal_Score'))
    signal_strength = safe_get(row, 'Signal', 'N/A')

    # If no signal data, return basic info
    if signal_score == 0.0 and signal_strength == 'N/A':
        return f"""
        <tr><th>Signal</th><td><span style="color: #666;">Not Calculated</span></td></tr>
        <tr><th>Status</th><td>Run analysis to get signal</td></tr>
        """

    # Color coding for signal strength
    signal_colors = {
        'STRONG BUY': '#22c55e',  # Green
        'BUY': '#3b82f6',         # Blue
        'MODERATE BUY': '#f59e0b', # Orange
        'HOLD': '#6b7280',        # Gray
        'WEAK': '#ef4444',        # Red
        'AVOID': '#dc2626'        # Dark Red
    }

    signal_color = signal_colors.get(signal_strength, '#6b7280')

    # Generate highlights based on V3 scoring pillars
    highlights = []

    # V3 Scoring focuses on 4 pillars: Earnings (35), Momentum (35), Trend (20), Liquidity (10)

    # Earnings Acceleration Highlights (35 pts)
    eps_lq = safe_float(safe_get(row, 'EPS Growth (Last Qtr vs. Same Qtr Prior Yr)'))
    eps_ttm = safe_float(safe_get(row, 'EPS Growth (TTM vs Prior TTM)'))
    eps_ny = safe_float(safe_get(row, 'EPS Growth (Proj Next Yr vs. This Yr)'))

    if eps_ttm >= 100:
        highlights.append("🚀 Triple-Digit EPS Growth")
    elif eps_ttm >= 50:
        highlights.append("💪 Strong EPS Acceleration")
    elif eps_ttm >= 25:
        highlights.append("📈 Good EPS Growth")

    if eps_lq >= 50:
        highlights.append("⚡ Quarterly EPS Surge")

    # Momentum & RS Highlights (35 pts)
    rs_rating = safe_float(safe_get(row, 'RS_Rating'))
    if rs_rating >= 90:
        highlights.append("🏆 Elite RS Rating (90+)")
    elif rs_rating >= 80:
        highlights.append("⭐ Strong RS Rating (80+)")
    elif rs_rating >= 70:
        highlights.append("📊 Good RS Rating (70+)")

    perf_52w = safe_float(safe_get(row, 'Price Performance (52 Weeks)'))
    if perf_52w >= 100:
        highlights.append("🎯 100%+ Annual Gain")
    elif perf_52w >= 50:
        highlights.append("📈 Strong Annual Performance")

    perf_13w = safe_float(safe_get(row, 'Price Performance (13 Weeks)'))
    if perf_13w >= 50:
        highlights.append("🔥 Hot Recent Momentum")
    elif perf_13w >= 25:
        highlights.append("📊 Good Recent Performance")

    # Trend Quality Highlights (20 pts)
    ttm = safe_get(row, 'TTM')
    if ttm and str(ttm).lower() in ['true', '1', 'yes']:
        highlights.append("🎯 TTM Squeeze Active")

    # Liquidity Highlights (10 pts)
    volume = safe_float(safe_get(row, '$Volume'))
    if volume >= 100:
        highlights.append("🌊 Massive Volume ($100M+)")
    elif volume >= 50:
        highlights.append("💧 High Volume ($50M+)")
    elif volume >= 25:
        highlights.append("📊 Good Volume ($25M+)")

    # Add score-based summary if available
    signal_score = safe_float(safe_get(row, 'Signal_Score'))
    if signal_score >= 80:
        highlights.insert(0, "🚀 Elite Momentum + Growth")
    elif signal_score >= 65:
        highlights.insert(0, "⭐ Strong Momentum Play")
    elif signal_score >= 50:
        highlights.insert(0, "📈 Solid Growth Story")
    elif signal_score >= 35:
        highlights.insert(0, "📊 Mixed Signals")
    elif signal_score > 0:
        highlights.insert(0, "⚠️ Weak Fundamentals")

    # If no highlights, add some basic info
    if not highlights:
        highlights.append("📊 Standard metrics")
        if rs_rating > 0:
            highlights.append(f"RS Rating: {rs_rating:.0f}")

    highlights_html = ""
    for highlight in highlights[:4]:  # Limit to 4 highlights
        highlights_html += f"<tr><td colspan='2' style='padding: 3px 8px; font-size: 0.9em; color: #374151; border-left: 3px solid #e5e7eb; background-color: #f9fafb;'>{_html_escape(highlight)}</td></tr>"

    return f"""
    <tr><th>Signal</th><td><strong style="color: {signal_color};">{_html_escape(signal_strength)}</strong></td></tr>
    <tr><th>Score</th><td>{signal_score:.1f}/100</td></tr>
    {highlights_html}
    """

class _LikeRequestHandler(BaseHTTPRequestHandler):
    def _write_cors_headers(self):
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')

    def do_OPTIONS(self):
        self.send_response(200)
        self._write_cors_headers()
        self.end_headers()

    def do_GET(self):
        parsed = urlparse(self.path)
        if parsed.path == '/like':
            qs = parse_qs(parsed.query)
            ticker = (qs.get('t') or qs.get('ticker') or [''])[0].strip().upper()
            if not ticker:
                self.send_response(400)
                self._write_cors_headers()
                self.end_headers()
                self.wfile.write(b'Missing ticker parameter t or ticker')
                return

            try:
                from .data_manager import _append_like
                _append_like(ticker)
                self.send_response(200)
                self._write_cors_headers()
                self.send_header('Content-Type', 'text/html; charset=utf-8')
                self.end_headers()
                self.wfile.write(f"<html><body><h3>Liked {ticker}</h3><p>You may close this tab.</p></body></html>".encode('utf-8'))
            except Exception as e:
                self.send_response(500)
                self._write_cors_headers()
                self.end_headers()
                self.wfile.write(f"Error: {e}".encode('utf-8'))
            return

        if parsed.path == '/dislike':
            qs = parse_qs(parsed.query)
            ticker = (qs.get('t') or qs.get('ticker') or [''])[0].strip().upper()
            if not ticker:
                self.send_response(400)
                self._write_cors_headers()
                self.end_headers()
                self.wfile.write(b'Missing ticker parameter t or ticker')
                return

            try:
                from .data_manager import _remove_like
                _remove_like(ticker)
                self.send_response(200)
                self._write_cors_headers()
                self.send_header('Content-Type', 'text/html; charset=utf-8')
                self.end_headers()
                self.wfile.write(f"<html><body><h3>Disliked {ticker}</h3><p>You may close this tab.</p></body></html>".encode('utf-8'))
            except Exception as e:
                self.send_response(500)
                self._write_cors_headers()
                self.end_headers()
                self.wfile.write(f"Error: {e}".encode('utf-8'))
            return

        self.send_response(404)
        self._write_cors_headers()
        self.end_headers()
        self.wfile.write(b'Not Found')
        return

        qs = parse_qs(parsed.query)
        ticker = (qs.get('t') or qs.get('ticker') or [''])[0].strip().upper()
        if not ticker:
            self.send_response(400)
            self._write_cors_headers()
            self.end_headers()
            self.wfile.write(b'Missing ticker parameter t or ticker')
            return

        try:
            from .data_manager import _append_like
            _append_like(ticker)
            self.send_response(200)
            self._write_cors_headers()
            self.send_header('Content-Type', 'text/html; charset=utf-8')
            self.end_headers()
            self.wfile.write(f"<html><body><h3>Liked {ticker}</h3><p>You may close this tab.</p></body></html>".encode('utf-8'))
        except Exception as e:
            self.send_response(500)
            self._write_cors_headers()
            self.end_headers()
            self.wfile.write(f"Error: {e}".encode('utf-8'))

class _AppHandler(BaseHTTPRequestHandler):
    def _write_cors_headers(self):
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')

    def _set_html(self):
        self.send_header('Content-Type', 'text/html; charset=utf-8')
        self.send_header('Cache-Control', 'no-store')

    def _serve_static_file(self, file_path):
        """Serve a static file from the 'ui' directory."""
        try:
            # Construct a safe file path
            base_dir = os.path.abspath(os.path.dirname(__file__))
            static_path = os.path.abspath(os.path.join(base_dir, 'ui', file_path))

            # Prevent directory traversal attacks
            if not static_path.startswith(os.path.join(base_dir, 'ui')):
                self.send_response(404)
                self.end_headers()
                self.wfile.write(b'Not Found')
                return

            with open(static_path, 'rb') as f:
                self.send_response(200)
                mime_type, _ = mimetypes.guess_type(static_path)
                self.send_header('Content-Type', mime_type or 'application/octet-stream')
                self.end_headers()
                self.wfile.write(f.read())
        except FileNotFoundError:
            self.send_response(404)
            self.end_headers()
            self.wfile.write(b'Not Found')
        except Exception as e:
            self.send_response(500)
            self.end_headers()
            self.wfile.write(f'Error: {e}'.encode('utf-8'))

    def _render_template(self, template_name, context):
        """Read an HTML template, replace placeholders, and send it."""
        try:
            base_dir = os.path.dirname(__file__)
            template_path = os.path.join(base_dir, 'ui', template_name)
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()

            for key, value in context.items():
                content = content.replace(f'{{{{{key}}}}}', str(value))

            self.send_response(200)
            self._set_html()
            self.end_headers()
            self.wfile.write(content.encode('utf-8'))
        except FileNotFoundError:
            self.send_response(404)
            self.end_headers()
            self.wfile.write(b'Template not found')
        except Exception as e:
            self.send_response(500)
            self.end_headers()
            self.wfile.write(f'Error: {e}'.encode('utf-8'))

    def do_GET(self):
        parsed = urlparse(self.path)
        path = parsed.path
        qs = parse_qs(parsed.query)

        if path.startswith('/ui/'):
            self._serve_static_file(path[len('/ui/'):])
            return

        if path == '/refresh-chart-ids':
            # Endpoint to refresh dynamic chart IDs
            try:
                refresh_chart_ids_sync()
                self.send_response(200)
                self.send_header('Content-Type', 'application/json')
                self._write_cors_headers()
                self.end_headers()

                response = {
                    'success': True,
                    'daily_id': DAILY_CHART_ID,
                    'weekly_id': WEEKLY_CHART_ID,
                    'message': 'Chart IDs refreshed successfully'
                }
                self.wfile.write(json.dumps(response).encode('utf-8'))
                return
            except Exception as e:
                self.send_response(500)
                self.send_header('Content-Type', 'application/json')
                self._write_cors_headers()
                self.end_headers()

                response = {
                    'success': False,
                    'error': str(e),
                    'message': 'Failed to refresh chart IDs'
                }
                self.wfile.write(json.dumps(response).encode('utf-8'))
                return

        if path == '/chart':
            def val(r, k):
                v = r.get(k)
                return '' if v is None else str(v)

            # Build chart data
            sector_data = {}
            industry_data = {}
            sub_industry_data = {}

            for row in CURRENT_TICKERS or []:
                sector = val(row, 'Sector').strip()
                industry = val(row, 'Industry').strip()
                sub_industry = val(row, 'Sub-Industry').strip()

                if sector:
                    sector_data[sector] = sector_data.get(sector, 0) + 1
                if industry:
                    industry_data[industry] = industry_data.get(industry, 0) + 1
                if sub_industry:
                    sub_industry_data[sub_industry] = sub_industry_data.get(sub_industry, 0) + 1

            # Sort by count and take top items
            top_sectors = sorted(sector_data.items(), key=lambda x: x[1], reverse=True)[:15]
            top_industries = sorted(industry_data.items(), key=lambda x: x[1], reverse=True)[:20]
            top_sub_industries = sorted(sub_industry_data.items(), key=lambda x: x[1], reverse=True)[:25]

            def build_bar_chart(data, title, color):
                if not data:
                    return f"<div class='chart-section'><h2>{title}</h2><p>No data available</p></div>"

                max_count = max(count for _, count in data)
                bars = []

                for name, count in data:
                    width_pct = (count / max_count) * 100 if max_count > 0 else 0
                    bars.append(f"""
                        <div class="bar-row">
                            <div class="bar-label">{_html_escape(name)}</div>
                            <div class="bar-container">
                                <div class="bar-fill" style="width: {width_pct}%; background-color: {color};"></div>
                                <div class="bar-count">{count}</div>
                            </div>
                        </div>
                    """)

                return f"""
                <div class="chart-section">
                    <h2>{title}</h2>
                    <div class="bar-chart">
                        {''.join(bars)}
                    </div>
                </div>
                """

            sectors_chart = build_bar_chart(top_sectors, "Top Sectors", "#4f46e5")
            industries_chart = build_bar_chart(top_industries, "Top Industries", "#059669")
            sub_industries_chart = build_bar_chart(top_sub_industries, "Top Sub-Industries", "#dc2626")

            context = {
                'total_stocks': len(CURRENT_TICKERS or []),
                'total_sectors': len(sector_data),
                'total_industries': len(industry_data),
                'total_sub_industries': len(sub_industry_data),
                'sectors_chart': sectors_chart,
                'industries_chart': industries_chart,
                'sub_industries_chart': sub_industries_chart,
            }
            self._render_template('chart.html', context)
            return

        if path == '/app':
            likes = set(_read_likes())

            show_liked_only = (qs.get('liked') or [''])[0] in ('1','true','yes','Y')
            filter_sector = (qs.get('sector') or [''])[0].strip()
            filter_industry = (qs.get('industry') or [''])[0].strip()
            filter_sub_industry = (qs.get('sub_industry') or [''])[0].strip()
            filter_signal = (qs.get('signal') or [''])[0].strip()

            def val(r, k):
                v = r.get(k)
                return '' if v is None else str(v)

            # Build cascading filter counts for dropdowns
            def build_cascading_filter_counts():
                # Always show all sectors
                sector_counts = {}
                for row in CURRENT_TICKERS or []:
                    sector = val(row, 'Sector').strip()
                    if sector:
                        sector_counts[sector] = sector_counts.get(sector, 0) + 1

                # Filter industries based on selected sector
                industry_counts = {}
                for row in CURRENT_TICKERS or []:
                    row_sector = val(row, 'Sector').strip()
                    industry = val(row, 'Industry').strip()

                    # Only include industries from the selected sector (or all if no sector selected)
                    if industry and (not filter_sector or row_sector == filter_sector):
                        industry_counts[industry] = industry_counts.get(industry, 0) + 1

                # Filter sub-industries based on selected sector AND industry
                sub_industry_counts = {}
                for row in CURRENT_TICKERS or []:
                    row_sector = val(row, 'Sector').strip()
                    row_industry = val(row, 'Industry').strip()
                    sub_industry = val(row, 'Sub-Industry').strip()

                    # Only include sub-industries from the selected sector and industry
                    sector_match = not filter_sector or row_sector == filter_sector
                    industry_match = not filter_industry or row_industry == filter_industry

                    if sub_industry and sector_match and industry_match:
                        sub_industry_counts[sub_industry] = sub_industry_counts.get(sub_industry, 0) + 1

                # Build signal counts (independent of other filters for now)
                signal_counts = {}
                for row in CURRENT_TICKERS or []:
                    signal = val(row, 'Signal').strip()
                    if signal and signal != 'N/A':
                        signal_counts[signal] = signal_counts.get(signal, 0) + 1

                # Sort by count descending
                return (
                    sorted(sector_counts.items(), key=lambda x: x[1], reverse=True),
                    sorted(industry_counts.items(), key=lambda x: x[1], reverse=True),
                    sorted(sub_industry_counts.items(), key=lambda x: x[1], reverse=True),
                    sorted(signal_counts.items(), key=lambda x: x[1], reverse=True)
                )

            sector_counts, industry_counts, sub_industry_counts, signal_counts = build_cascading_filter_counts()

            iterable = CURRENT_TICKERS or []
            if show_liked_only:
                liked_syms = {str(s).upper() for s in likes}
                iterable = [r for r in iterable if str(r.get('Symbol') or '').upper() in liked_syms]

            # Apply sector/industry/signal filters
            if filter_sector:
                iterable = [r for r in iterable if val(r, 'Sector').strip() == filter_sector]
            if filter_industry:
                iterable = [r for r in iterable if val(r, 'Industry').strip() == filter_industry]
            if filter_sub_industry:
                iterable = [r for r in iterable if val(r, 'Sub-Industry').strip() == filter_sub_industry]
            if filter_signal:
                iterable = [r for r in iterable if val(r, 'Signal').strip() == filter_signal]

            # Build filter dropdowns HTML
            def build_dropdown_options(counts, current_filter, param_name):
                options = f'<option value="">All ({sum(count for _, count in counts)})</option>'
                for name, count in counts:
                    selected = 'selected' if name == current_filter else ''
                    options += f'<option value="{_html_escape(name)}" {selected}>{_html_escape(name)} ({count})</option>'
                return options

            sector_options = build_dropdown_options(sector_counts, filter_sector, 'sector')
            industry_options = build_dropdown_options(industry_counts, filter_industry, 'industry')
            sub_industry_options = build_dropdown_options(sub_industry_counts, filter_sub_industry, 'sub_industry')
            signal_options = build_dropdown_options(signal_counts, filter_signal, 'signal')

            rows = []
            for idx, row in enumerate(iterable):
                sym = str(row.get('Symbol') or '').upper()
                if not sym:
                    continue
                liked = '✓ Liked' if sym in likes else 'Like'

                sector = val(row, 'Sector')
                industry = val(row, 'Industry')
                sub_industry = val(row, 'Sub-Industry')

                # Create single line sector/industry display
                sector_line = f"{_html_escape(sector)} | {_html_escape(industry)} | {_html_escape(sub_industry)}"

                # Generate signal highlights
                signal_highlights = _get_signal_highlights(row)

                # Get description if available
                description = val(row, 'Description')
                description_row = ""
                if description and description != 'N/A' and description.strip():
                    # Show 150 chars with full description on hover
                    if len(description) > 150:
                        truncated_desc = description[:150] + '...'
                        description_row = f'<tr><th>Description</th><td style="font-size: 0.85em; color: #4b5563; cursor: help;" title="{_html_escape(description)}">{_html_escape(truncated_desc)}</td></tr>'
                    else:
                        description_row = f'<tr><th>Description</th><td style="font-size: 0.85em; color: #4b5563;">{_html_escape(description)}</td></tr>'

                info_html = f"""
                  <table class=info>
                    <tr><th>Classification</th><td style="font-size: 0.9em;">{sector_line}</td></tr>
                    {description_row}
                    {signal_highlights}
                  </table>
                """

                rows.append(f"""
                <div class="card" data-idx="{idx}">
                  <div class="title">
                    <span>{_html_escape(sym)}</span>
                  </div>
                  <div class="charts">
                    <section class="chart">
                      <header>Daily {_html_escape(sym)}</header>
                      <figure>
                        <img class="lazy-image" data-ticker="{_html_escape(sym)}" data-type="d" alt="Daily {_html_escape(sym)}" src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='600' height='400'%3E%3Crect width='100%25' height='100%25' fill='%23f1f5f9'/%3E%3Ctext x='50%25' y='50%25' font-family='system-ui' font-size='16' fill='%2364748b' text-anchor='middle' dy='0.3em'%3ELoading Daily Chart...%3C/text%3E%3C/svg%3E">
                      </figure>
                    </section>
                    <section class="chart">
                      <header>Weekly {_html_escape(sym)}</header>
                      <figure>
                        <img class="lazy-image" data-ticker="{_html_escape(sym)}" data-type="w" alt="Weekly {_html_escape(sym)}" src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='600' height='400'%3E%3Crect width='100%25' height='100%25' fill='%23f1f5f9'/%3E%3Ctext x='50%25' y='50%25' font-family='system-ui' font-size='16' fill='%2364748b' text-anchor='middle' dy='0.3em'%3ELoading Weekly Chart...%3C/text%3E%3C/svg%3E">
                      </figure>
                    </section>
                  </div>
                  <div class="info-wrap">
                    {info_html}
                  </div>
                  <div class="actions">
                    <button onclick="like('{_html_escape(sym)}', this)">{liked}</button>
                    <button onclick="dislike('{_html_escape(sym)}', this)">Dislike</button>
                    <button onclick="refreshChartIds()" title="Refresh StockCharts IDs">🔄 Charts</button>
                  </div>
                </div>
                """)

            context = {
                'sector_options': sector_options,
                'industry_options': industry_options,
                'sub_industry_options': sub_industry_options,
                'signal_options': signal_options,
                'rows': ''.join(rows),
            }
            self._render_template('app.html', context)
            return

        if path == '/img':
            sym = (qs.get('t') or [''])[0].upper()
            typ = (qs.get('type') or ['d'])[0]
            if not sym:
                self.send_response(400)
                self.end_headers()
                self.wfile.write(b'Missing t')
                return
            # Check if we have valid chart IDs
            if not DAILY_CHART_ID or not WEEKLY_CHART_ID:
                self.send_response(503)
                self.end_headers()
                self.wfile.write(b'Chart IDs not available. Please wait for initialization or refresh.')
                return

            daily_url = f'https://stockcharts.com/c-sc/sc?s=ticker&p=D&yr=0&mn=6&dy=0&i={{}}&r=tdy'.format(DAILY_CHART_ID)
            weekly_url = f'https://stockcharts.com/c-sc/sc?s=ticker&p=W&yr=2&mn=6&dy=0&i={{}}&r=tdy'.format(WEEKLY_CHART_ID)
            src = daily_url if typ.lower().startswith('d') else weekly_url
            src = src.replace('tdy', str(time.time())).replace('ticker', sym)
            try:
                session = get_session(use_standard_requests=False)
                r = session.get(src, stream=True)
                if not getattr(r, 'ok', False):
                    session = get_session(use_standard_requests=True)
                    r = session.get(src, stream=True)
                    if not r.ok:
                        self.send_response(502)
                        self.end_headers()
                        self.wfile.write(f'Upstream error: {getattr(r, "status_code", "?")}'.encode('utf-8'))
                        return
                self.send_response(200)
                self.send_header('Content-Type', 'image/png')
                self.send_header('Cache-Control', 'no-store')
                self.end_headers()
                for block in r.iter_content(1024):
                    if not block:
                        break
                    self.wfile.write(block)
            except Exception as e:
                self.send_response(500)
                self.end_headers()
                self.wfile.write(f'Error: {e}'.encode('utf-8'))
            return

        if path == '/likes':
            likes = ','.join(_read_likes())
            self.send_response(200)
            self.send_header('Content-Type', 'text/plain; charset=utf-8')
            self.end_headers()
            self.wfile.write(likes.encode('utf-8'))
            return

        if path == '/save_week':
            try:
                _ensure_data_dirs()
                import csv
                snapshot_date = _get_snapshot_date_yyyy_mm_dd()
                tickers = ','.join(_read_likes())
                exists = os.path.exists(LIKES_CSV_PATH)
                with open(LIKES_CSV_PATH, 'a', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f, delimiter='|')
                    if not exists:
                        writer.writerow(['Date','Ticker List'])
                    writer.writerow([snapshot_date, tickers])
                self.send_response(200)
                self.end_headers()
                self.wfile.write(b'Saved week entry to liked_tickers_history.csv')
            except Exception as e:
                self.send_response(500)
                self.end_headers()
                self.wfile.write(f'Error: {e}'.encode('utf-8'))
            return

        self.send_response(404)
        self.end_headers()
        self.wfile.write(b'Not found')

def start_server(port, handler):
    server = ThreadingHTTPServer(('127.0.0.1', port), handler)
    thread = threading.Thread(target=server.serve_forever, name=f'{handler.__name__}Server', daemon=True)
    thread.start()
    return server, thread

def start_like_server():
    """Start a background HTTP server to receive Like clicks and serve the UI."""
    from .data_manager import _ensure_data_dirs
    _ensure_data_dirs()

    # Initialize dynamic chart IDs in the background
    def init_chart_ids():
        """Initialize chart IDs in a separate thread"""
        try:
            print("🎯 Initializing dynamic StockCharts IDs...")
            asyncio.run(setup_dynamic_chart_ids())
        except Exception as e:
            print(f"❌ Failed to initialize chart IDs: {e}")

    # Start chart ID initialization in background thread
    chart_init_thread = threading.Thread(target=init_chart_ids, name="ChartIDInit", daemon=True)
    chart_init_thread.start()

    like_server, _ = start_server(LIKE_SERVER_PORT, _LikeRequestHandler)
    app_server, _ = start_server(LIKE_SERVER_PORT + 1, _AppHandler)
    return (like_server, app_server)
