#!/usr/bin/env python3
"""
Test AAPL and ADRs with the new fallback RS calculation
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

import pandas as pd
import yfinance as yf
from datetime import datetime, timedelta

def test_stocks_with_fallback():
    """Test AAPL and ADRs with the new fallback logic"""
    print("=== Testing AAPL and ADRs with Fallback Logic ===")
    
    try:
        # Import the updated functions
        sys.path.append('src/fidelity/chart_analysis/rs')
        from build_rs_stocks import weighted_rs_score, weighted_rs_score_fallback
        
        # Test stocks: AAPL (should have lots of data) + ADRs
        test_symbols = ['AAPL', 'BABA', 'PONY', 'JD', 'BIDU', 'NIO', 'XPEV']
        
        # Get benchmark data (SPY)
        print("Getting benchmark data...")
        spy = yf.Ticker("SPY")
        end_date = datetime.now()
        start_date = end_date - timedelta(days=365)
        spy_hist = spy.history(start=start_date, end=end_date)
        
        print(f"SPY data: {len(spy_hist)} days")
        print("\n" + "="*80)
        
        results = []
        
        for symbol in test_symbols:
            print(f"\n--- Testing {symbol} ---")
            try:
                ticker = yf.Ticker(symbol)
                hist = ticker.history(start=start_date, end=end_date)
                
                if len(hist) == 0:
                    print(f"  ❌ No data available")
                    continue
                
                # Align dates
                common_dates = hist.index.intersection(spy_hist.index)
                stock_close = hist.loc[common_dates, 'Close']
                spy_close = spy_hist.loc[common_dates, 'Close']
                
                print(f"  Data days: {len(stock_close)}")
                print(f"  Latest price: ${stock_close.iloc[-1]:.2f}")
                
                # Test original function
                original_score = weighted_rs_score(stock_close, spy_close)
                print(f"  Original RS: {original_score if original_score is not None else 'None (insufficient data)'}")
                
                # Test fallback function
                fallback_score = weighted_rs_score_fallback(stock_close, spy_close)
                print(f"  Fallback RS: {fallback_score if fallback_score is not None else 'None'}")
                
                # Test combined logic (what build_rs_stocks.py will use)
                final_score = original_score
                method_used = "Original"
                if final_score is None:
                    final_score = fallback_score
                    method_used = "Fallback"
                
                if final_score is not None:
                    print(f"  ✓ Final RS Score: {final_score:.2f} (using {method_used})")
                    results.append({
                        'Symbol': symbol,
                        'Data_Days': len(stock_close),
                        'RS_Score': final_score,
                        'Method': method_used,
                        'Price': stock_close.iloc[-1]
                    })
                else:
                    print(f"  ❌ Both methods failed")
                    
            except Exception as e:
                print(f"  ❌ Error: {e}")
        
        # Summary table
        if results:
            print("\n" + "="*80)
            print("SUMMARY TABLE")
            print("="*80)
            df = pd.DataFrame(results)
            print(df.to_string(index=False, float_format='%.2f'))
            
            # Check which method was used
            original_count = len(df[df['Method'] == 'Original'])
            fallback_count = len(df[df['Method'] == 'Fallback'])
            
            print(f"\nMethod Usage:")
            print(f"  Original method: {original_count} stocks")
            print(f"  Fallback method: {fallback_count} stocks")
            
            return True
        else:
            print("\n❌ No successful calculations")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_requirements():
    """Check data requirements for each method"""
    print("\n=== Data Requirements Analysis ===")
    
    test_symbols = ['AAPL', 'PONY']
    
    for symbol in test_symbols:
        print(f"\n--- {symbol} Data Analysis ---")
        try:
            ticker = yf.Ticker(symbol)
            end_date = datetime.now()
            start_date = end_date - timedelta(days=365)
            hist = ticker.history(start=start_date, end=end_date)
            
            data_days = len(hist)
            print(f"  Available data: {data_days} days")
            
            # Original method requirements
            original_min = 253  # max([63, 126, 189, 252]) + 1
            original_ok = data_days >= original_min
            print(f"  Original method (needs {original_min}): {'✓ OK' if original_ok else '❌ INSUFFICIENT'}")
            
            # Fallback method requirements  
            fallback_min = 120  # As defined in our fallback function
            fallback_ok = data_days >= fallback_min
            print(f"  Fallback method (needs {fallback_min}): {'✓ OK' if fallback_ok else '❌ INSUFFICIENT'}")
            
        except Exception as e:
            print(f"  ❌ Error: {e}")

if __name__ == "__main__":
    print("=" * 80)
    print("TESTING AAPL AND ADRs WITH FALLBACK RS CALCULATION")
    print("=" * 80)
    
    success = test_stocks_with_fallback()
    test_data_requirements()
    
    print("\n" + "=" * 80)
    print("NEXT STEPS")
    print("=" * 80)
    if success:
        print("✓ Fallback logic is working!")
        print("  Ready to run full RS analysis:")
        print("  cd src/fidelity/chart_analysis/rs && python build_rs_stocks.py")
    else:
        print("❌ Issues found - check output above")
