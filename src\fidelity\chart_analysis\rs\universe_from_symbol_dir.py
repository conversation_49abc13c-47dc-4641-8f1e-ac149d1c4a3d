import re
import pandas as pd

NASDAQLISTED_URL = "https://www.nasdaqtrader.com/dynamic/SymDir/nasdaqlisted.txt"
OTHERLISTED_URL  = "https://www.nasdaqtrader.com/dynamic/SymDir/otherlisted.txt"

CODE_MAP = {"A":"NYSE American","N":"NYSE","P":"NYSE Arca","Z":"Cboe BZX","V":"IEX"}
ALLOWED_EXCHANGES = {"NASDAQ","NYSE","NYSE American","NYSE Arca"}

def to_yahoo_symbol(sym: str) -> str:
    s = (sym or "").strip().upper().replace("/", "-")
    m = re.fullmatch(r"([A-Z0-9]+)\.([A-Z])", s)
    return f"{m.group(1)}-{m.group(2)}" if m else s

def _load_txt(url: str) -> pd.DataFrame:
    df = pd.read_csv(url, sep="|", dtype=str)
    if "Symbol" in df.columns:
        df = df[~df["Symbol"].str.upper().eq("FILE CREATION TIME")]
    return df

def _base_filters(df: pd.DataFrame) -> pd.DataFrame:
    if "ETF" in df.columns:
        df = df[df["ETF"].str.upper() == "N"]
    if "Test Issue" in df.columns:
        df = df[df["Test Issue"].str.upper() == "N"]
    if "NextShares" in df.columns:
        df = df[df["NextShares"].str.upper() != "Y"]
    return df

def load_stocks_only_from_symbol_dir(allow_adr=True, allow_reit=True,
                                     allowed_exchanges=ALLOWED_EXCHANGES) -> pd.DataFrame:
    nas = _load_txt(NASDAQLISTED_URL)
    if "Financial Status" in nas.columns:
        # N = Normal. Other values are D, E, Q for deficient, delinquent, bankrupt.
        # We also want to keep NaN values, which are treated as normal.
        non_normal = {'D', 'E', 'Q', 'G', 'H', 'J', 'K'}
        nas = nas[~nas["Financial Status"].str.upper().isin(non_normal)]
    nas = _base_filters(nas).copy()
    oth = _base_filters(_load_txt(OTHERLISTED_URL)).copy()

    if "NASDAQ Symbol" in oth.columns:
        oth["Symbol"] = oth["NASDAQ Symbol"]
    elif "CQS Symbol" in oth.columns:
        oth["Symbol"] = oth["CQS Symbol"]
    elif "ACT Symbol" in oth.columns:
        oth["Symbol"] = oth["ACT Symbol"]

    nas["Exchange"] = "NASDAQ"
    if "Exchange" in oth.columns:
        oth["Exchange"] = oth["Exchange"].map(CODE_MAP).fillna("OTHER")
    else:
        oth["Exchange"] = "OTHER"

    df = pd.concat([nas[["Symbol","Security Name","Exchange"]],
                    oth[["Symbol","Security Name","Exchange"]]], ignore_index=True)

    if allowed_exchanges:
        df = df[df["Exchange"].isin(allowed_exchanges)]

    df["Symbol"] = df["Symbol"].astype(str).map(to_yahoo_symbol)
    df = df[df["Symbol"] != ""].drop_duplicates(subset=["Symbol"]).copy()

    name = df["Security Name"].fillna("").str.upper()

    EXCLUDE = (
        " PREFERRED", " PFD", " PREFERENCE",
        " WARRANT", " RIGHTS", " RIGHT ", " RT ", " RTS ",
        " UNIT", " UNITS",
        " NOTE", " NOTES", " BOND", " DEBENTURE",
        " TRUST", " WHEN ISSUED", " WI ",
        " EQUITY UNITS", " TRACKING STOCK",
        " OTC",
    )
    mask_excl = ~name.str.contains("|".join(map(re.escape, EXCLUDE)))

    KEEP = ["COMMON STOCK", "ORDINARY SHARES"]
    if allow_adr:
        KEEP += ["AMERICAN DEPOSITARY SHARES", " ADS "]
    if allow_reit:
        KEEP += [" REIT"]

    mask_keep = name.str.contains("|".join(map(re.escape, KEEP)))

    trimmed = df[mask_keep & mask_excl].copy()
    trimmed.rename(columns={"Security Name":"SecurityName"}, inplace=True)

    return trimmed[["Symbol","Exchange","SecurityName"]].reset_index(drop=True)

def load_stocks_strict_exchanges_only(allow_adr=True, allow_reit=True) -> pd.DataFrame:
    STRICT_EXCHANGES = {"NASDAQ", "NYSE"}
    return load_stocks_only_from_symbol_dir(allow_adr, allow_reit, STRICT_EXCHANGES)
